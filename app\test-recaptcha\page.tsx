"use client";

import React, { useState } from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/ReCaptcha";

export default function TestReCaptchaPage() {
  const [token, setToken] = useState<string | null>(null);

  return (
    <div style={{ minHeight: "100vh", display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center", background: "#181c24" }}>
      <h1 style={{ color: "#fff", marginBottom: 24 }}>Google reCAPTCHA v2 Test</h1>
      <div style={{ marginBottom: 24 }}>
        <ReCaptcha onVerify={setToken} />
      </div>
      <div style={{ color: token ? "#4caf50" : "#fff" }}>
        {token ? (
          <>
            <strong>Token:</strong>
            <div style={{ wordBreak: "break-all", marginTop: 8 }}>{token}</div>
          </>
        ) : (
          "Complete the CAPTCHA to see the token."
        )}
      </div>
    </div>
  );
} 