"use client"

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { X } from 'lucide-react'
import { Button } from "@/components/ui/button"
import Image from 'next/image'
import {
  Dialog,
  DialogContent,
  DialogClose,
} from "@/components/ui/dialog"

export function SaleOfferModal() {
  const [isOpen, setIsOpen] = useState(false)
  useEffect(() => {
    const timer = setTimeout(() => setIsOpen(true), 3000)
    return () => clearTimeout(timer)
  }, [])

  // Only show Two-Step prices for 25K, 50K, 100K (50K in center)
  const accounts = [
    { size: '25K', twostep: { real: '$91.25', discounted: '$73' } },
    { size: '50K', twostep: { real: '$150.00', discounted: '$45' } },
    { size: '100K', twostep: { real: '$240.00', discounted: '$72' } },
  ];

  // 3D effect for desktop: leftmost +15deg, center 0deg (scale up), rightmost -15deg
  const get3DEffect = (idx, length) => {
    if (typeof window !== 'undefined' && window.innerWidth < 768) return { rotateY: 0, scale: 1, zIndex: 1 };
    if (idx === 0) return { rotateY: 15, scale: 0.95, zIndex: 2 };
    if (idx === length - 1) return { rotateY: -15, scale: 0.95, zIndex: 2 };
    if (idx === Math.floor(length / 2)) return { rotateY: 0, scale: 1.08, zIndex: 3 };
    return { rotateY: 0, scale: 1, zIndex: 1 };
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="bg-[#181c23] border-none shadow-2xl w-full max-w-md md:max-w-2xl rounded-2xl p-0 flex items-center justify-center overflow-y-auto">
          <motion.div
          initial={{ opacity: 0, scale: 0.97 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, ease: [0.22, 1, 0.36, 1] }}
          className="relative w-full flex flex-col items-center px-4 py-6 md:px-8 md:py-8"
        >
          <DialogClose className="absolute top-4 right-4 text-gray-400 hover:text-white z-20">
            <X className="w-6 h-6" />
          </DialogClose>

          {/* Logo in top left corner */}
          <div className="absolute top-4 left-4 z-20">
            <Image src="/logo.svg" alt="Funded Horizon Logo" width={60} height={16} className="h-auto opacity-90" />
          </div>

          {/* Headline */}
          <div className="flex flex-col items-center mb-6 mt-2">
            <span className="text-xs md:text-sm text-[#b2e3ff] tracking-widest font-semibold mb-1 uppercase">LIMITED TIME OFFER</span>
            <span className="text-2xl md:text-3xl font-extrabold text-center text-white leading-tight">
              <span className="block text-orange-400 drop-shadow">GET UP TO</span>
              <span className="block text-4xl md:text-5xl text-orange-400 drop-shadow-lg">70% OFF!</span>
                        </span>
            </div>

          {/* Account Cards - 3D coverflow, no scroll */}
          <div className="w-full mb-6 flex flex-col md:flex-row md:gap-6 gap-4 md:justify-center md:items-end">
            {accounts.map((account, idx) => (
                <motion.div
                    key={account.size}
                initial={get3DEffect(idx, accounts.length)}
                animate={get3DEffect(idx, accounts.length)}
                transition={{ type: 'spring', stiffness: 200, damping: 20 }}
                className={`flex flex-col items-center justify-center bg-[#202a36] rounded-2xl px-7 py-6 w-full md:w-[170px] md:min-w-[170px] border-2 shadow-2xl transition-all duration-200 cursor-pointer hover:border-yellow-400/80
                  ${idx === 1 ? 'border-cyan-400/80' : 'border-green-400/60'}`}
                style={{ marginTop: typeof window !== 'undefined' && window.innerWidth >= 768 && idx === 1 ? '-16px' : '0' }}
              >
                <span className="text-lg md:text-xl font-bold text-orange-400 mb-2 tracking-wider">{account.size}</span>
                <span className="text-[13px] md:text-sm text-blue-300 font-semibold text-center mb-0.5">Two-Step Account</span>
                <span className="text-xs text-gray-400 line-through">Real: <span className="text-gray-400">{account.twostep.real}</span></span>
                <span className="text-xl md:text-2xl font-bold text-green-400">Discounted: {account.twostep.discounted}</span>
              </motion.div>
            ))}
          </div>

            <Button 
            className="w-full max-w-xs mx-auto bg-gradient-to-r from-orange-400 via-yellow-300 to-green-400 text-black font-extrabold text-base md:text-lg py-2 md:py-2.5 rounded-lg hover:brightness-110 transition-all duration-300 shadow-xl mt-2 mb-1 tracking-tight z-10 flex items-center justify-center gap-2"
              onClick={() => {
                setIsOpen(false);
                window.location.href = '/signup';
              }}
            >
                Start Your Trading Journey
            </Button>
          <Button variant="link" onClick={() => setIsOpen(false)} className="text-gray-400 mt-1 hover:text-white text-xs md:text-sm z-10">
                Decline Offer
            </Button>
          </motion.div>
      </DialogContent>
    </Dialog>
  )
}
