# reCAPTCHA v3 Setup Guide

## Overview
This application now includes reCAPTCHA v3 protection on the signin page to prevent automated attacks and ensure only human users can access the system.

## Setup Instructions

### 1. Get reCAPTCHA Site Key
1. Go to [Google reCAPTCHA Admin Console](https://www.google.com/recaptcha/admin)
2. Click "Create" to add a new site
3. Choose "reCAPTCHA v3"
4. Add your domain(s) where the application will be hosted
5. Accept the terms and click "Submit"
6. Copy the "Site Key" (this is what you'll use in the frontend)

### 2. Configure Environment Variables
Create a `.env.local` file in the root directory of your project and add:

```env
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=your_actual_site_key_here
```

### 3. Backend Verification (Optional but Recommended)
For complete security, you should also verify the reCAPTCHA token on your backend:

1. Get the "Secret Key" from the reCAPTCHA admin console
2. In your backend API endpoint (`/api/auth/login`), verify the token:

```python
# Example Python verification
import requests

def verify_recaptcha(token, secret_key):
    response = requests.post('https://www.google.com/recaptcha/api/siteverify', {
        'secret': secret_key,
        'response': token
    })
    result = response.json()
    return result.get('success', False) and result.get('score', 0) > 0.5
```

## How It Works

1. **Invisible Protection**: reCAPTCHA v3 runs invisibly in the background
2. **Score-based**: Returns a score from 0.0 (bot) to 1.0 (human)
3. **Automatic Execution**: Runs automatically when the form loads
4. **Token Verification**: Sends the verification token with the login request

## Current Implementation

- The reCAPTCHA component is integrated into the signin page
- It automatically executes when the page loads
- The verification status is displayed to the user
- The submit button is disabled until verification is complete
- The token is sent with the login request for backend verification

## Security Benefits

- Prevents automated login attempts
- Reduces spam and abuse
- Provides user behavior analysis
- Maintains good user experience (invisible to users)

## Troubleshooting

If reCAPTCHA is not working:
1. Check that your site key is correctly set in `.env.local`
2. Ensure your domain is added to the reCAPTCHA admin console
3. Check browser console for any JavaScript errors
4. Verify that the reCAPTCHA script is loading properly

## Test Key
The current implementation uses a test key that always returns a score of 0.9. Replace it with your actual site key for production use. 