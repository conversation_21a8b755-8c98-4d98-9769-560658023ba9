"use client";
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion'
import {
  Shield,
  BarChart4,
  Clock,
  TrendingDown,
  Lock,
  Sliders,
  <PERSON><PERSON><PERSON>,
  Timer,
  Wallet,
  CheckCircle
} from 'lucide-react'

function useIsIPhone() {
  const [isIPhone, setIsIPhone] = useState(false);
  useEffect(() => {
    setIsIPhone(/iPhone|iPad|iPod/.test(navigator.userAgent));
  }, []);
  return isIPhone;
}

// Top-level server component
export function RiskManagement() {
  const isIPhone = useIsIPhone();
  return <RiskManagementClient isIPhone={isIPhone} />;
}

function RiskManagementClient({ isIPhone }) {
  const rules = [
    {
      title: "Daily Drawdown",
      description: "Maximum 5% loss allowed in a single trading day",
      icon: BarChart4,
      value: "5%"
    },
    {
      title: "Max Drawdown",
      description: "Overall account drawdown limited to 10%",
      icon: TrendingDown,
      value: "10%"
    },
    {
      title: "Profit Target",
      description: "8-10% profit target depending on challenge type",
      icon: PieChart,
      value: "8-10%"
    },
    {
      title: "Leverage Limits",
      description: "Maximum leverage of 1:100 on major pairs",
      icon: Sliders,
      value: "1:100"
    },
    {
      title: "Min Trading Days",
      description: "Minimum 5 trading days required for most challenges",
      icon: Clock,
      value: "5 Days"
    },
    {
      title: "Profit Split",
      description: "Up to 90% profit sharing on funded accounts",
      icon: Wallet,
      value: "90%"
    }
  ]

  return (
    <section className={`${isIPhone ? 'min-h-screen-ios' : 'min-h-screen'} relative section-tight bg-gradient-to-b from-[#0A0F1C] to-black`}>
      <div className="container mx-auto px-4">
        {/* Simple Header */}
        <div className="text-center mb-8 md:mb-12">
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-orange-500 via-blue-500 to-blue-600 bg-clip-text text-transparent mb-6">
            Risk Management Rules
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our comprehensive risk management framework ensures sustainable trading success and account protection
          </p>
        </div>

        {/* Rules Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {rules.map((rule, index) => (
            <div
              key={index}
              className="bg-gradient-to-br from-[#1a1f2d] to-[#252a3a] rounded-xl border border-white/10 overflow-hidden hover:border-orange-500/30 transition-all duration-300"
            >
              {/* Card Header */}
              <div className="p-6 bg-gradient-to-r from-blue-600 to-blue-800">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 rounded-lg bg-white/10 backdrop-blur-sm">
                    <rule.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-white">{rule.value}</div>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{rule.title}</h3>
                <p className="text-white/80 text-sm">{rule.description}</p>
              </div>

              {/* Card Content */}
              <div className="p-6">
                <div className="flex items-center gap-2 text-green-400">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm font-medium">Active Rule</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-orange-500/10 to-blue-500/10 rounded-xl p-8 border border-orange-500/20">
            <div className="flex items-center justify-center mb-4">
              <Shield className="w-12 h-12 text-orange-400" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">
              Your Security is Our Priority
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Our risk management rules are designed to protect your capital and ensure long-term trading success.
              All traders must adhere to these guidelines to maintain their funded accounts.
            </p>
            <button className="bg-gradient-to-r from-orange-500 to-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:brightness-110 transition-all duration-300">
              Learn More About Rules
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}