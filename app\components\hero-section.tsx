"use client";

import { <PERSON><PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { TrendingUp, ArrowRight, Shield, Award, Globe, DollarSign, BarChart3, Target } from 'lucide-react'
import { useState, useEffect } from 'react'
import Image from 'next/image'

function useIsIPhone() {
  const [isIPhone, setIsIPhone] = useState(false);
  useEffect(() => {
    setIsIPhone(/iPhone|iPad|iPod/.test(navigator.userAgent));
  }, []);
  return isIPhone;
}

// 3D Animated Trading Sphere Component - Optimized for iPhone
const TradingSphere3D = ({ isBackground = false, isMobile = false, isIPhone = false }) => {
  // Ultra-simplified version for iPhone to prevent overheating
  if (isIPhone) {
    return (
      <div className={`relative w-full h-full flex items-center justify-center ${isBackground ? 'absolute inset-0 opacity-15' : ''}`}>
        <div className={`relative ${isBackground ? 'w-20 h-20' : 'w-28 h-28'}`}>
          {/* Static circles for iPhone - no animations to prevent heating */}
          <div className="absolute inset-0 rounded-full border-2 border-orange-500/20" />
          <div className="absolute inset-2 rounded-full border border-blue-500/20" />
          <div className="absolute inset-4 rounded-full border border-green-500/20" />
          {/* Single subtle pulse animation only */}
          <motion.div
            animate={{
              scale: [1, 1.05, 1],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute inset-6 rounded-full border border-orange-400/30"
          />
        </div>
      </div>
    )
  }

  // Simplified version for other mobile devices
  if (isMobile) {
    return (
      <div className={`relative w-full h-full flex items-center justify-center ${isBackground ? 'absolute inset-0 opacity-20' : ''}`}>
        <motion.div
          animate={{
            rotate: 360,
          }}
          transition={{
            duration: 40, // Slower animation for better performance
            repeat: Infinity,
            ease: "linear"
          }}
          className={`relative ${isBackground ? 'w-24 h-24' : 'w-32 h-32'}`}
        >
          <div className="absolute inset-0 rounded-full border-2 border-orange-500/30" />
          <div className="absolute inset-4 rounded-full border-2 border-blue-500/30" />
          <div className="absolute inset-8 rounded-full border-2 border-green-500/30" />
        </motion.div>
      </div>
    )
  }

  return (
    <div className={`relative w-full h-full flex items-center justify-center ${isBackground ? 'absolute inset-0 opacity-30' : ''}`}>
      {/* Main 3D Sphere */}
      <motion.div
        animate={{ 
          rotateY: 360,
          rotateX: 180,
        }}
        transition={{ 
          duration: 30, // Slower animation for better performance
          repeat: Infinity, 
          ease: "linear" 
        }}
        className={`relative ${isBackground ? 'w-48 h-48 sm:w-64 sm:h-64 md:w-80 md:h-80' : 'w-[16rem] h-[16rem] sm:w-[20rem] sm:h-[20rem] md:w-[28rem] md:h-[28rem] lg:w-96 lg:h-96'}`}
        style={{
          transformStyle: 'preserve-3d',
        }}
      >
        {/* Enhanced Sphere layers with more complexity */}
        {[...Array(isBackground ? 4 : 6)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute inset-0 rounded-full border-2 ${i % 3 === 0 ? 'border-orange-500/40' : i % 3 === 1 ? 'border-blue-500/40' : 'border-green-500/40'}`}
            style={{
              transform: `rotateY(${i * 30}deg) rotateX(${i * 25}deg) rotateZ(${i * 15}deg)`,
              transformStyle: 'preserve-3d',
            }}
            animate={{
              rotateZ: [0, 360],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 20 + i * 1.5, // Slower animations
              repeat: Infinity,
              ease: "linear",
              delay: i * 0.3,
            }}
          />
        ))}
        
        {/* Inner rotating elements with enhanced effects */}
        <motion.div
          animate={{ 
            rotate: 360,
            scale: [1, 1.05, 1],
          }}
          transition={{ 
            duration: 18, // Slower animation
            repeat: Infinity, 
            ease: "linear" 
          }}
          className="absolute inset-8 rounded-full border-2 border-blue-500/50 shadow-lg shadow-blue-500/20"
        />
        
        <motion.div
          animate={{ 
            rotate: -360,
            scale: [1, 0.95, 1],
          }}
          transition={{ 
            duration: 25, // Slower animation
            repeat: Infinity, 
            ease: "linear" 
          }}
          className="absolute inset-16 rounded-full border-2 border-green-500/40 shadow-lg shadow-green-500/20"
        />
        
        {!isBackground && (
          <motion.div
            animate={{ 
              rotate: 360,
              scale: [1, 1.1, 1],
            }}
            transition={{ 
              duration: 35, // Slower animation
              repeat: Infinity, 
              ease: "linear" 
            }}
            className="absolute inset-24 rounded-full border border-purple-500/30 shadow-lg shadow-purple-500/20"
          />
        )}
        
        {/* Enhanced floating trading icons with glow effects */}
        <motion.div
          animate={{ 
            y: [0, -25, 0],
            rotate: [0, 360],
            scale: [1, 1.2, 1],
          }}
          transition={{ 
            duration: 8, // Slower animation
            repeat: Infinity, 
            ease: "easeInOut" 
          }}
          className="absolute top-4 left-1/2 -translate-x-1/2"
        >
          <div className="relative">
            <DollarSign className={`${isBackground ? 'w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8' : 'w-6 h-6 sm:w-8 sm:h-8'} text-orange-400 drop-shadow-lg`} />
            <div className="absolute inset-0 w-full h-full bg-orange-400/20 rounded-full blur-md animate-pulse"></div>
          </div>
        </motion.div>
        
        <motion.div
          animate={{ 
            y: [0, 25, 0],
            rotate: [0, -360],
            scale: [1, 1.2, 1],
          }}
          transition={{ 
            duration: 10, // Slower animation
            repeat: Infinity, 
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute bottom-4 left-1/2 -translate-x-1/2"
        >
          <div className="relative">
            <TrendingUp className={`${isBackground ? 'w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8' : 'w-6 h-6 sm:w-8 sm:h-8'} text-green-400 drop-shadow-lg`} />
            <div className="absolute inset-0 w-full h-full bg-green-400/20 rounded-full blur-md animate-pulse"></div>
          </div>
        </motion.div>
        
        {!isBackground && (
          <>
            <motion.div
              animate={{ 
                x: [0, 25, 0],
                rotate: [0, 360],
                scale: [1, 1.2, 1],
              }}
              transition={{ 
                duration: 9, // Slower animation
                repeat: Infinity, 
                ease: "easeInOut",
                delay: 2
              }}
              className="absolute top-1/2 right-4 -translate-y-1/2"
            >
              <div className="relative">
                <BarChart3 className="w-6 h-6 sm:w-8 sm:h-8 text-blue-400 drop-shadow-lg" />
                <div className="absolute inset-0 w-full h-full bg-blue-400/20 rounded-full blur-md animate-pulse"></div>
              </div>
            </motion.div>
            
            <motion.div
              animate={{ 
                x: [0, -25, 0],
                rotate: [0, -360],
                scale: [1, 1.2, 1],
              }}
              transition={{ 
                duration: 11, // Slower animation
                repeat: Infinity, 
                ease: "easeInOut",
                delay: 3
              }}
              className="absolute top-1/2 left-4 -translate-y-1/2"
            >
              <div className="relative">
                <Target className="w-6 h-6 sm:w-8 sm:h-8 text-purple-400 drop-shadow-lg" />
                <div className="absolute inset-0 w-full h-full bg-purple-400/20 rounded-full blur-md animate-pulse"></div>
              </div>
            </motion.div>
          </>
        )}
        
        {/* Enhanced central glow effect */}
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-orange-500/10 via-blue-500/10 to-green-500/10 blur-xl animate-pulse"></div>
      </motion.div>
    </div>
  )
}

// Trading Animation Background Component
const TradingAnimation = () => {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Animated background elements */}
      {/* Removed Secure Card */}
      {/* Removed Support Card */}
      {/* Removed Country Card */}
      {/* Removed Profit Split Card */}
      {/* Grid pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>
    </div>
  )
}

export default function Hero() {
  const [isMobile, setIsMobile] = useState(false)
  const isIPhone = useIsIPhone();

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // iPhone-specific performance optimization
  useEffect(() => {
    if (isIPhone) {
      document.documentElement.style.setProperty('--animation-duration', '0.5s');
      document.documentElement.style.setProperty('--reduce-motion', '1');
    }
  }, [isIPhone])

  return (
    <section className={`relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-b from-[#0A0F1C] via-[#0F1A2E] to-[#121E36]`}>  
      {/* 3D Animated Glassy Orb (Professional) */}
      <motion.div
        className="absolute inset-0 flex items-center justify-center z-0 pointer-events-none"
        initial={{ rotate: 0, scale: 1 }}
        animate={{ rotate: 360, scale: [1, 1.04, 1] }}
        transition={{ repeat: Infinity, duration: 32, ease: 'linear' }}
        style={{ filter: 'blur(0.2px)' }}
      >
        <div className="relative w-[420px] h-[420px] md:w-[600px] md:h-[600px] flex items-center justify-center">
          {/* Main glassy orb */}
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-white/10 via-blue-400/10 to-orange-300/10 shadow-2xl" style={{ boxShadow: '0 0 120px 40px #3b82f655, 0 0 80px 20px #fbbf2455' }} />
          {/* Subtle inner ring for 3D effect */}
          <motion.div
            className="absolute inset-10 rounded-full border-8 border-blue-400/20 border-t-orange-400/40 border-b-green-400/40"
            initial={{ rotate: 0 }}
            animate={{ rotate: 360 }}
            transition={{ repeat: Infinity, duration: 24, ease: 'linear' }}
            style={{ boxShadow: '0 0 40px 8px #3b82f655' }}
          />
          {/* Faint grid overlay for depth */}
          <div className="absolute inset-0 rounded-full overflow-hidden pointer-events-none">
            <svg width="100%" height="100%" viewBox="0 0 600 600" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-full opacity-10">
              <defs>
                <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                  <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#fff" strokeWidth="1" opacity="0.2" />
                </pattern>
              </defs>
              <rect width="600" height="600" fill="url(#grid)" />
            </svg>
          </div>
          {/* Glow overlay */}
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-orange-400/10 via-blue-500/10 to-green-400/10 blur-2xl opacity-70" />
            </div>
      </motion.div>

      {/* Layered Glowing Animation (Subtle, for depth) */}
      <div className="absolute inset-0 flex items-center justify-center z-0 pointer-events-none">
        <div className="absolute w-[600px] h-[600px] md:w-[800px] md:h-[800px] rounded-full bg-gradient-to-br from-orange-400/20 via-blue-500/10 to-green-400/20 blur-3xl opacity-60 animate-pulse" style={{filter:'blur(60px)'}} />
        <div className="absolute w-80 h-80 left-1/4 top-1/4 rounded-full bg-blue-500/10 blur-2xl animate-pulse" style={{filter:'blur(40px)'}} />
        <div className="absolute w-60 h-60 right-1/4 bottom-1/4 rounded-full bg-orange-400/10 blur-2xl animate-pulse" style={{filter:'blur(30px)'}} />
        {/* Floating dots, more subtle */}
        <motion.div animate={{y: [0, -12, 0]}} transition={{duration: 8, repeat: Infinity, ease: 'easeInOut'}} className="absolute left-1/3 top-1/2 w-6 h-6 rounded-full bg-white/10 blur-lg" />
        <motion.div animate={{y: [0, 12, 0]}} transition={{duration: 9, repeat: Infinity, ease: 'easeInOut'}} className="absolute right-1/3 bottom-1/3 w-5 h-5 rounded-full bg-orange-400/10 blur-lg" />
        <motion.div animate={{x: [0, 18, 0]}} transition={{duration: 10, repeat: Infinity, ease: 'easeInOut'}} className="absolute left-1/2 top-1/4 w-4 h-4 rounded-full bg-blue-400/10 blur-lg" />
            </div>
            
      {/* Centered Heading and Content */}
      <div className="relative z-10 flex flex-col items-center justify-center w-full max-w-3xl px-4 text-center mx-auto">
              <motion.h1
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
          className="text-5xl sm:text-6xl md:text-7xl font-extrabold leading-tight tracking-tight mb-6"
        >
          <span className="block text-white mb-2 tracking-tight">TRADE LIKE</span>
          <span className="block bg-gradient-to-r from-orange-500 via-yellow-400 to-blue-600 bg-clip-text text-transparent tracking-tight">A PRO</span>
              </motion.h1>
              <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
          className="text-lg sm:text-xl md:text-2xl text-gray-300 max-w-2xl mb-8 leading-relaxed"
              >
          Get funded up to <span className="text-orange-400 font-bold">$2.5M</span> with our professional trading platform. Join thousands of successful traders worldwide.
              </motion.p>
              <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
              >
                <Button
                  size="lg"
            className="bg-gradient-to-r from-orange-500 to-blue-600 hover:from-orange-600 hover:to-blue-700 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto"
                >
            Start Trading Challenge
                </Button>
                <Button
                  variant="outline"
                  size="lg"
            className="border-2 border-blue-500/50 text-blue-400 hover:bg-blue-500/10 font-semibold px-8 py-3 rounded-xl transition-all duration-300 w-full sm:w-auto"
                >
                  Learn More
                </Button>
              </motion.div>
      </div>
    </section>
  )
}
