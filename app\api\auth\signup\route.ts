export const runtime = 'nodejs';
import { NextRequest, NextResponse } from 'next/server';
import { validateInput, sanitizeInput, generateCSRFToken, validateCSRFToken, rateLimit, setSecurityHeaders } from '@/lib/node-security';
import { getApiUrl } from '@/app/config/api';

// Rate limiting configuration for signup
const signupRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 signup attempts per hour
  message: 'Too many signup attempts, please try again later'
});

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await signupRateLimit(request);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: rateLimitResult.message },
        { status: 429, headers: setSecurityHeaders() }
      );
    }

    // Validate CSRF token
    const csrfToken = request.headers.get('x-csrf-token');
    if (!csrfToken || !validateCSRFToken(csrfToken)) {
      return NextResponse.json(
        { error: 'Invalid CSRF token' },
        { status: 403, headers: setSecurityHeaders() }
      );
    }

    // Parse and validate request body
    const formData = await request.formData();
    const userData = {
      name: formData.get('name') as string,
      username: formData.get('username') as string,
      email: formData.get('email') as string,
      password: formData.get('password') as string,
      country: formData.get('country') as string,
      contact: formData.get('contact') as string,
      address: formData.get('address') as string,
      referralCode: formData.get('referralCode') as string,
      recaptcha_token: formData.get('recaptcha_token') as string
    };

    // Comprehensive input validation
    const validations = {
      name: validateInput(userData.name, {
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: /^[a-zA-Z\s]+$/
      }),
      username: validateInput(userData.username, {
        required: true,
        minLength: 3,
        maxLength: 20,
        pattern: /^[a-zA-Z0-9_]+$/
      }),
      email: validateInput(userData.email, {
        required: true,
        type: 'email',
        maxLength: 254
      }),
      password: validateInput(userData.password, {
        required: true,
        minLength: 8,
        maxLength: 128,
        customValidation: (value) => {
          const hasUpperCase = /[A-Z]/.test(value);
          const hasLowerCase = /[a-z]/.test(value);
          const hasNumbers = /\d/.test(value);
          const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
          
          if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
            return { 
              isValid: false, 
              error: 'Password must contain uppercase, lowercase, number, and special character' 
            };
          }
          return { isValid: true };
        }
      }),
      country: validateInput(userData.country, {
        required: true,
        minLength: 2,
        maxLength: 50
      }),
      contact: validateInput(userData.contact, {
        required: true,
        minLength: 10,
        maxLength: 20,
        pattern: /^\+?[\d\s\-\(\)]+$/
      }),
      address: validateInput(userData.address, {
        required: true,
        minLength: 10,
        maxLength: 200
      }),
      referralCode: validateInput(userData.referralCode, {
        required: false,
        maxLength: 20,
        pattern: /^[a-zA-Z0-9_-]*$/
      }),
      recaptcha_token: validateInput(userData.recaptcha_token, {
        required: true,
        minLength: 10,
        maxLength: 1000
      })
    };

    // Check for validation errors
    const errors = Object.entries(validations)
      .filter(([_, validation]) => !validation.isValid)
      .map(([field, validation]) => ({ field, error: validation.error }));

    if (errors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', details: errors },
        { status: 400, headers: setSecurityHeaders() }
      );
    }

    // Sanitize all inputs
    const sanitizedData = {
      name: sanitizeInput(userData.name),
      username: sanitizeInput(userData.username),
      email: sanitizeInput(userData.email),
      password: sanitizeInput(userData.password),
      country: sanitizeInput(userData.country),
      contact: sanitizeInput(userData.contact),
      address: sanitizeInput(userData.address),
      referralCode: sanitizeInput(userData.referralCode),
      recaptcha_token: sanitizeInput(userData.recaptcha_token)
    };

    // Forward request to backend API with additional security headers
    const backendResponse = await fetch(getApiUrl('signup'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': request.headers.get('user-agent') || '',
        'X-Forwarded-For': request.headers.get('x-forwarded-for') || '',
        'X-Real-IP': request.headers.get('x-real-ip') || '',
        'X-CSRF-Token': csrfToken
      },
      body: JSON.stringify({
        username: sanitizedData.username,
        email: sanitizedData.email,
        password: sanitizedData.password,
        name: sanitizedData.name,
        phone_no: sanitizedData.contact,
        country: sanitizedData.country,
        address: sanitizedData.address,
        referral_code: sanitizedData.referralCode,
        recaptcha_token: sanitizedData.recaptcha_token
      })
    });

    if (backendResponse.ok) {
      const data = await backendResponse.json();
      
      // Generate new CSRF token for next request
      const newCSRFToken = generateCSRFToken();
      
      // Log successful signup for security monitoring
      console.info(`Successful signup for email: ${sanitizedData.email}`, {
        timestamp: new Date().toISOString(),
        ip: request.headers.get('x-forwarded-for') || request.ip,
        userAgent: request.headers.get('user-agent'),
        username: sanitizedData.username
      });
      
      // Create secure response with security headers
      const response = NextResponse.json(data, {
        status: 200,
        headers: {
          ...setSecurityHeaders(),
          'Set-Cookie': `csrf_token=${newCSRFToken}; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=3600`,
          'X-CSRF-Token': newCSRFToken
        }
      });

      return response;
    } else {
      const errorData = await backendResponse.json();
      
      // Log failed signup attempt for security monitoring
      console.warn(`Failed signup attempt for email: ${sanitizedData.email}`, {
        timestamp: new Date().toISOString(),
        ip: request.headers.get('x-forwarded-for') || request.ip,
        userAgent: request.headers.get('user-agent'),
        status: backendResponse.status,
        error: errorData.detail
      });

      return NextResponse.json(
        { error: errorData.detail || 'Registration failed' },
        { status: backendResponse.status, headers: setSecurityHeaders() }
      );
    }
  } catch (error) {
    console.error('Signup API error:', error);
    
    // Log security event
    console.error('Security event: Signup API error', {
      timestamp: new Date().toISOString(),
      ip: request.headers.get('x-forwarded-for') || request.ip,
      userAgent: request.headers.get('user-agent'),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: setSecurityHeaders() }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      ...setSecurityHeaders(),
      'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-CSRF-Token',
      'Access-Control-Max-Age': '86400'
    }
  });
} 