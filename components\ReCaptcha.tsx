"use client";

import React from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "react-google-recaptcha";

interface ReCaptchaProps {
  onVerify: (token: string | null) => void;
  className?: string;
}

const SITE_KEY = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || "6LdoZXYrAAAAAPEoYxxbmr_LH9kgt1vpsjkpdlU7";

const ReCaptcha: React.FC<ReCaptchaProps> = ({ onVerify, className }) => {
  return (
    <div className={className}>
      <ReCAPTCHA
        sitekey={SITE_KEY}
        onChange={onVerify}
        theme="light"
      />
    </div>
  );
};

export default ReCaptcha; 