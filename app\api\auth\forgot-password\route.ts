export const runtime = 'nodejs';
import { NextRequest, NextResponse } from 'next/server';
import { validateInput, sanitizeInput, generateCSRFToken, validateCSRFToken, rateLimit, setSecurityHeaders } from '@/lib/node-security';
import { getApiUrl } from '@/app/config/api';

// Rate limiting configuration for forgot password
const forgotPasswordRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 forgot password attempts per hour
  message: 'Too many forgot password attempts, please try again later'
});

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await forgotPasswordRateLimit(request);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: rateLimitResult.message },
        { status: 429, headers: setSecurityHeaders() }
      );
    }

    // Validate CSRF token
    const csrfToken = request.headers.get('x-csrf-token');
    if (!csrfToken || !validateCSRFToken(csrfToken)) {
      return NextResponse.json(
        { error: 'Invalid CSRF token' },
        { status: 403, headers: setSecurityHeaders() }
      );
    }

    // Parse and validate request body
    const formData = await request.formData();
    const userData = {
      email: formData.get('email') as string,
      recaptcha_token: formData.get('recaptcha_token') as string
    };

    // Comprehensive input validation
    const validations = {
      email: validateInput(userData.email, {
        required: true,
        type: 'email',
        maxLength: 254
      }),
      recaptcha_token: validateInput(userData.recaptcha_token, {
        required: true,
        minLength: 10,
        maxLength: 1000
      })
    };

    // Check for validation errors
    const errors = Object.entries(validations)
      .filter(([_, validation]) => !validation.isValid)
      .map(([field, validation]) => ({ field, error: validation.error }));

    if (errors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', details: errors },
        { status: 400, headers: setSecurityHeaders() }
      );
    }

    // Sanitize all inputs
    const sanitizedData = {
      email: sanitizeInput(userData.email),
      recaptcha_token: sanitizeInput(userData.recaptcha_token)
    };

    // Forward request to backend API with additional security headers
    const backendResponse = await fetch(getApiUrl('auth/forgot-password'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': request.headers.get('user-agent') || '',
        'X-Forwarded-For': request.headers.get('x-forwarded-for') || '',
        'X-Real-IP': request.headers.get('x-real-ip') || '',
        'X-CSRF-Token': csrfToken
      },
      body: JSON.stringify({
        email: sanitizedData.email,
        recaptcha_token: sanitizedData.recaptcha_token
      })
    });

    if (backendResponse.ok) {
      const data = await backendResponse.json();
      
      // Generate new CSRF token for next request
      const newCSRFToken = generateCSRFToken();
      
      // Log successful forgot password request for security monitoring
      console.info(`Successful forgot password request for email: ${sanitizedData.email}`, {
        timestamp: new Date().toISOString(),
        ip: request.headers.get('x-forwarded-for') || request.ip,
        userAgent: request.headers.get('user-agent')
      });
      
      // Create secure response with security headers
      const response = NextResponse.json(data, {
        status: 200,
        headers: {
          ...setSecurityHeaders(),
          'Set-Cookie': `csrf_token=${newCSRFToken}; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=3600`,
          'X-CSRF-Token': newCSRFToken
        }
      });

      return response;
    } else {
      const errorData = await backendResponse.json();
      
      // Log failed forgot password attempt for security monitoring
      console.warn(`Failed forgot password attempt for email: ${sanitizedData.email}`, {
        timestamp: new Date().toISOString(),
        ip: request.headers.get('x-forwarded-for') || request.ip,
        userAgent: request.headers.get('user-agent'),
        status: backendResponse.status,
        error: errorData.detail
      });

      return NextResponse.json(
        { error: errorData.detail || 'Forgot password request failed' },
        { status: backendResponse.status, headers: setSecurityHeaders() }
      );
    }
  } catch (error) {
    console.error('Forgot password API error:', error);
    
    // Log security event
    console.error('Security event: Forgot password API error', {
      timestamp: new Date().toISOString(),
      ip: request.headers.get('x-forwarded-for') || request.ip,
      userAgent: request.headers.get('user-agent'),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: setSecurityHeaders() }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      ...setSecurityHeaders(),
      'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-CSRF-Token',
      'Access-Control-Max-Age': '86400'
    }
  });
} 