"use client";
import { useState, ChangeEvent, FormEvent, useEffect } from "react";
import { Navbar } from "../component/navbar";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { countries } from "countries-list";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import SecureInput from "../components/SecureInput";
import { 
  Bitcoin, 
  DollarSign, 
  TrendingUp, 
  ArrowUp, 
  ArrowDown, 
  Shield, 
  Lock, 
  Mail, 
  User, 
  Globe, 
  MapPin,
  Eye,
  EyeOff,
  CheckCircle,
  AlertCircle,
  Building2,
  Users,
  Award,
  Zap,
  UserCheck,
  Globe2
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { getApiUrl, getFakeApiUrl } from "../config/api"
import secureStorageAPI from '@/app/lib/secureStorage';
import <PERSON><PERSON>aptcha from '@/components/ReCaptcha';

interface FormData {
  name: string;
  username: string;
  email: string;
  password: string;
  country: string;
  contact: string;
  address: string;
  zip: string;
  postalCode: string;
  referralCode: string;
}

interface InputFieldProps {
  label: string;
  type: string;
  placeholder: string;
  value: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  name: string;
  className?: string;
  isSelect?: boolean;
  options?: { value: string; label: string }[];
  required?: boolean;
  icon?: React.ReactNode;
  error?: string;
  showPasswordToggle?: boolean;
  onTogglePassword?: () => void;
}

function InputField({
  label,
  type,
  placeholder,
  value,
  onChange,
  name,
  className = "",
  isSelect = false,
  options = [],
  required = false,
  icon,
  error,
  showPasswordToggle = false,
  onTogglePassword
}: InputFieldProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="flex flex-col gap-2"
    >
      <label className="text-sm font-semibold text-gray-200 tracking-wide">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
        )}
        {isSelect ? (
          <select
            value={value}
            onChange={(e) => onChange(e as unknown as ChangeEvent<HTMLInputElement>)}
            name={name}
            className={`
              w-full px-4 py-3.5 rounded-xl bg-gray-900/50 text-white border 
              ${error ? 'border-red-500' : 'border-gray-700'} 
              focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 
              transition-all duration-300
              ${icon ? 'pl-12' : 'pl-4'}
              ${className}
            `}
            required={required}
          >
            <option value="">Select a country</option>
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        ) : (
          <input
            type={type}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            name={name}
            className={`
              w-full px-4 py-3.5 rounded-xl bg-gray-900/50 text-white border 
              ${error ? 'border-red-500' : 'border-gray-700'} 
              focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 
              transition-all duration-300 placeholder-gray-500
              ${icon ? 'pl-12' : 'pl-4'}
              ${showPasswordToggle ? 'pr-12' : ''}
              ${className}
            `}
            required={required}
          />
        )}
        {showPasswordToggle && (
          <button
            type="button"
            onClick={onTogglePassword}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
          >
            {type === 'password' ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        )}
      </div>
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 text-red-400 text-sm"
        >
          <AlertCircle size={16} />
          {error}
        </motion.div>
      )}
    </motion.div>
  );
}

const SecurityBadge = ({ icon: Icon, title, description }: { icon: any, title: string, description: string }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-gray-900/50 to-gray-800/50 border border-gray-700/50 hover:border-gray-600/50 transition-all duration-300"
  >
    <div className="p-2.5 rounded-lg bg-blue-500/10 border border-blue-500/20">
      <Icon size={20} className="text-blue-400" />
    </div>
    <div>
      <h4 className="text-gray-200 font-semibold text-sm">{title}</h4>
      <p className="text-xs text-gray-400">{description}</p>
    </div>
  </motion.div>
);

const TradingPairs = () => {
  const [pairs, setPairs] = useState([
    { pair: 'BTC/USD', price: '43,521.23', change: '+2.34%', isUp: true, volume: '$2.4B', lastUpdate: Date.now() },
    { pair: 'ETH/USD', price: '2,234.56', change: '-1.23%', isUp: false, volume: '$1.8B', lastUpdate: Date.now() },
    { pair: 'SOL/USD', price: '98.45', change: '+5.67%', isUp: true, volume: '$890M', lastUpdate: Date.now() },
    { pair: 'XRP/USD', price: '0.5234', change: '-0.89%', isUp: false, volume: '$450M', lastUpdate: Date.now() },
  ]);

  const [lastUpdate, setLastUpdate] = useState(Date.now());

  const generateRandomChange = (currentPrice: number) => {
    const change = (Math.random() - 0.5) * 0.02;
    const newPrice = currentPrice * (1 + change);
    return {
      price: newPrice.toFixed(2),
      change: `${(change * 100).toFixed(2)}%`,
      isUp: change > 0
    };
  };

  const updatePrices = () => {
    setPairs(prevPairs => 
      prevPairs.map(pair => {
        const currentPrice = parseFloat(pair.price.replace(/,/g, ''));
        const { price, change, isUp } = generateRandomChange(currentPrice);
        
        return {
          ...pair,
          price: parseFloat(price).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
          change,
          isUp,
          lastUpdate: Date.now()
        };
      })
    );
    setLastUpdate(Date.now());
  };

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null
    
    const startInterval = () => {
      intervalId = setInterval(updatePrices, 5000)
    }
    
    startInterval()
    
    return () => {
      if (intervalId) {
        clearInterval(intervalId)
        intervalId = null
      }
    }
  }, [])

  const formatTimeAgo = (timestamp: number) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    return `${minutes}m ago`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50 shadow-xl"
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-gray-100 flex items-center gap-3">
          <div className="p-2 rounded-lg bg-blue-500/10 border border-blue-500/20">
            <TrendingUp size={20} className="text-blue-400" />
          </div>
          Live Market Data
        </h3>
        <span className="text-xs text-gray-400 bg-gray-800/50 px-2 py-1 rounded-full">
          {formatTimeAgo(lastUpdate)}
        </span>
      </div>
      <div className="space-y-3">
        {pairs.map((pair, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-center justify-between p-3 rounded-xl bg-gray-800/30 hover:bg-gray-700/30 transition-all duration-300 border border-gray-700/30"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-500/10 border border-blue-500/20">
                <Bitcoin size={18} className="text-blue-400" />
              </div>
              <div>
                <span className="text-gray-200 font-semibold text-sm">{pair.pair}</span>
                <span className="text-xs text-gray-400 block">Vol: {pair.volume}</span>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <motion.span 
                key={pair.price}
                initial={{ scale: 1.2, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="text-gray-300 font-semibold text-sm"
              >
                ${pair.price}
              </motion.span>
              <motion.span 
                key={pair.change}
                initial={{ scale: 1.2, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className={`flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-medium ${
                  pair.isUp 
                    ? 'bg-green-500/10 text-green-400 border border-green-500/20' 
                    : 'bg-red-500/10 text-red-400 border border-red-500/20'
                }`}
              >
                {pair.isUp ? <ArrowUp size={12} /> : <ArrowDown size={12} />}
                {pair.change}
              </motion.span>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

const ProfessionalSpinner = () => (
  <div className="flex items-center justify-center">
    <div className="relative">
      <div className="w-5 h-5 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
      <div className="absolute inset-0 w-5 h-5 border-2 border-transparent border-t-blue-400 rounded-full animate-ping"></div>
    </div>
  </div>
);

const PasswordStrengthIndicator = ({ password }: { password: string }) => {
  const getStrength = () => {
    if (!password) return { score: 0, label: '', color: 'bg-gray-600' };
    
    let score = 0;
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    if (score <= 1) return { score, label: 'Weak', color: 'bg-red-500' };
    if (score <= 2) return { score, label: 'Fair', color: 'bg-orange-500' };
    if (score <= 3) return { score, label: 'Good', color: 'bg-yellow-500' };
    if (score <= 4) return { score, label: 'Strong', color: 'bg-green-500' };
    return { score, label: 'Very Strong', color: 'bg-emerald-500' };
  };

  const strength = getStrength();
  const width = (strength.score / 5) * 100;

  return (
    <div className="mt-2">
      <div className="flex items-center justify-between text-xs mb-1">
        <span className="text-gray-400">Password strength:</span>
        <span className={`font-medium ${strength.color.replace('bg-', 'text-')}`}>
          {strength.label}
        </span>
      </div>
      <div className="w-full bg-gray-700 rounded-full h-1.5">
        <div 
          className={`h-1.5 rounded-full transition-all duration-300 ${strength.color}`}
          style={{ width: `${width}%` }}
        ></div>
      </div>
    </div>
  );
};

export default function Signup() {
  const router = useRouter();
  const countryList = Object.entries(countries).map(([code, country]) => ({
    value: code,
    label: country.name,
  }));

  const [formData, setFormData] = useState<FormData>({
    name: "",
    username: "",
    email: "",
    password: "",
    country: "",
    contact: "",
    address: "",
    zip: "",
    postalCode: "",
    referralCode: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    
    if (!formData.name.trim()) {
      newErrors.name = "Full name is required";
    }
    
    if (!formData.username.trim()) {
      newErrors.username = "Username is required";
    } else if (formData.username.length < 3) {
      newErrors.username = "Username must be at least 3 characters";
    }
    
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }
    
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
    }
    
    if (!formData.country) {
      newErrors.country = "Please select your country";
    }
    
    if (!formData.contact) {
      newErrors.contact = "Contact number is required";
    }
    
    if (!formData.address.trim()) {
      newErrors.address = "Address is required";
    }
    
    if (!acceptedTerms) {
      newErrors.terms = "You must accept the terms and conditions";
    }

    if (!recaptchaToken) {
      newErrors.recaptcha = "Please complete the security verification";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    // Clear error when user starts typing
    if (errors[e.target.name]) {
      setErrors({ ...errors, [e.target.name]: "" });
    }
  };

  const handlePhoneChange = (value: any) => {
    setFormData({ ...formData, contact: String(value) });
    if (errors.contact) {
      setErrors({ ...errors, contact: "" });
    }
  };

  const handleRecaptcha = (token: string | null) => {
    setRecaptchaToken(token);
    if (errors.recaptcha && token) {
      setErrors({ ...errors, recaptcha: "" });
    }
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsLoading(true);
    
    const payload = {
      username: formData.username,
      email: formData.email,
      password: formData.password,
      name: formData.name,
      phone_no: formData.contact,
      country: formData.country,
      address: formData.address,
      referral_code: formData.referralCode,
      recaptcha_token: recaptchaToken || ''
    };

    try {
      const response = await fetch(getApiUrl('auth/signup'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-csrf-token': (typeof window !== 'undefined' && document.cookie.match(/csrf_token=([^;]+)/)?.[1]) || ''
        },
        body: JSON.stringify(payload)
      });

      if (response.ok) {
        const data = await response.json();
        secureStorageAPI.setItem('verification_email', formData.email);
        toast.success('Account created successfully! Please verify your email.');
        setTimeout(() => {
          router.push('/verify-email');
        }, 2000);
      } else {
        const errorData = await response.json();
        toast.error('Registration failed. Please try again.');
      }
    } catch (error) {
      toast.error('Connection error. Please check your internet connection.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Navbar />
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 p-4 md:p-6 lg:p-8 relative overflow-hidden">
        {/* Enhanced Background Pattern with Orange and Blue - Hidden on mobile */}
        <div className="hidden md:block absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.15),transparent_50%)]"></div>
        <div className="hidden md:block absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(249,115,22,0.12),transparent_50%)]"></div>
        <div className="hidden md:block absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(59,130,246,0.08),transparent_50%)]"></div>
        <div className="hidden md:block absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(249,115,22,0.1),transparent_50%)]"></div>
        
        {/* Animated Background Elements - Hidden on mobile */}
        <div className="hidden md:block absolute inset-0">
          <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-orange-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-blue-500/3 to-orange-500/3 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>
        
        {/* Custom Phone Input Styles */}
        <style jsx global>{`
          .phone-input-container .form-control {
            background: rgba(17, 24, 39, 0.5) !important;
            border: 1px solid rgb(55, 65, 81) !important;
            border-radius: 12px !important;
            color: white !important;
            font-size: 16px !important;
            padding: 14px 14px 14px 48px !important;
            transition: all 0.3s ease !important;
          }
          
          .phone-input-container .form-control:focus {
            border-color: rgb(59, 130, 246) !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
            outline: none !important;
          }
          
          .phone-input-container .flag-dropdown {
            background: rgba(17, 24, 39, 0.5) !important;
            border: 1px solid rgb(55, 65, 81) !important;
            border-radius: 12px 0 0 12px !important;
          }
          
          .phone-input-container .flag-dropdown:hover {
            background: rgba(31, 41, 55, 0.5) !important;
          }
          
          .phone-input-container .selected-flag {
            background: transparent !important;
            border-radius: 12px 0 0 12px !important;
          }
          
          .phone-input-container .country-list {
            background: rgb(17, 24, 39) !important;
            border: 1px solid rgb(55, 65, 81) !important;
            border-radius: 8px !important;
            color: white !important;
          }
          
          .phone-input-container .country-list .country {
            color: white !important;
          }
          
          .phone-input-container .country-list .country:hover {
            background: rgba(59, 130, 246, 0.1) !important;
          }
          
          .phone-input-container .country-list .highlight {
            background: rgba(59, 130, 246, 0.2) !important;
          }
        `}</style>
        
        <div className="w-full max-w-7xl mx-auto mt-20 grid grid-cols-1 lg:grid-cols-2 gap-12 relative z-10">
          {/* Signup Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-gradient-to-br from-gray-900/95 via-gray-800/90 to-gray-900/95 backdrop-blur-xl rounded-3xl shadow-2xl p-8 md:p-10 lg:p-12 border border-gray-700/50 order-2 lg:order-1 relative overflow-hidden"
          >
            {/* Form Background Pattern - Hidden on mobile */}
            <div className="hidden md:block absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-orange-500/5"></div>
            <div className="hidden md:block absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-transparent rounded-full blur-2xl"></div>
            <div className="hidden md:block absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-tr from-orange-500/10 to-transparent rounded-full blur-2xl"></div>
            
            <div className="relative z-10">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center mb-10"
              >
                <Image 
                  src="/logo.svg" 
                  alt="Funded Horizon Logo" 
                  width={200} 
                  height={50} 
                  className="mx-auto mb-8 h-auto"
                />
                <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-100 via-blue-100 to-orange-100 bg-clip-text text-transparent mb-4">
                  Create Your Account
                </h1>
                <p className="text-gray-300 text-lg font-medium">
                  Join our professional trading platform
                </p>
              </motion.div>

              <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <SecureInput
                    label="Full Name"
                    type="text"
                    placeholder="Enter your full name"
                    value={formData.name}
                    onChange={handleChange}
                    name="name"
                    icon={<User size={20} />}
                    error={errors.name}
                    validation={{
                      required: true,
                      minLength: 2,
                      maxLength: 50
                    }}
                  />
                </div>
                
                <SecureInput
                  label="Username"
                  type="text"
                  placeholder="Choose a username"
                  value={formData.username}
                  onChange={handleChange}
                  name="username"
                  icon={<UserCheck size={20} />}
                  error={errors.username}
                  validation={{
                    required: true,
                    minLength: 3,
                    maxLength: 20,
                    pattern: /^[a-zA-Z0-9_]+$/
                  }}
                />
                
                <SecureInput
                  label="Email Address"
                  type="email"
                  placeholder="Enter your email address"
                  value={formData.email}
                  onChange={handleChange}
                  name="email"
                  icon={<Mail size={20} />}
                  error={errors.email}
                  validation={{
                    required: true,
                    customValidation: (value) => {
                      if (!/\S+@\S+\.\S+/.test(value)) {
                        return { isValid: false, error: 'Please enter a valid email address' };
                      }
                      return { isValid: true };
                    }
                  }}
                />
                
                <SecureInput
                  label="Password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Create a strong password"
                  value={formData.password}
                  onChange={handleChange}
                  name="password"
                  icon={<Lock size={20} />}
                  error={errors.password}
                  validation={{
                    required: true,
                    minLength: 8,
                    customValidation: (value) => {
                      const hasUpperCase = /[A-Z]/.test(value);
                      const hasLowerCase = /[a-z]/.test(value);
                      const hasNumbers = /\d/.test(value);
                      const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
                      
                      if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
                        return { 
                          isValid: false, 
                          error: 'Password must contain uppercase, lowercase, number, and special character' 
                        };
                      }
                      return { isValid: true };
                    }
                  }}
                />
                <PasswordStrengthIndicator password={formData.password} />
                
                <div className="md:col-span-2">
                  <label className="text-sm font-semibold text-gray-200 tracking-wide mb-2 block">
                    Country <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                      <Globe2 size={20} />
                    </div>
                    <select
                      value={formData.country}
                      onChange={(e) => handleChange(e as unknown as ChangeEvent<HTMLInputElement>)}
                      name="country"
                      className="w-full px-4 py-3.5 rounded-xl bg-gray-900/50 text-white border border-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 pl-12"
                      required={true}
                    >
                      <option value="">Select a country</option>
                      {countryList.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  {errors.country && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center gap-2 text-red-400 text-sm mt-2"
                    >
                      <AlertCircle size={16} />
                      {errors.country}
                    </motion.div>
                  )}
                </div>
                
                <div className="md:col-span-2">
                  <label className="text-sm font-semibold text-gray-200 tracking-wide mb-2 block">
                    Contact Number <span className="text-red-500">*</span>
                  </label>
                  <PhoneInput
                    country={'us'}
                    value={formData.contact}
                    onChange={handlePhoneChange}
                    inputClass="w-full px-4 py-3.5 rounded-xl bg-gray-900/50 text-white border border-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 pl-12"
                    containerClass="phone-input-container"
                  />
                  {errors.contact && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center gap-2 text-red-400 text-sm mt-2"
                    >
                      <AlertCircle size={16} />
                      {errors.contact}
                    </motion.div>
                  )}
                </div>
                
                <div className="md:col-span-2">
                  <SecureInput
                    label="Address"
                    type="text"
                    placeholder="Enter your full address"
                    value={formData.address}
                    onChange={handleChange}
                    name="address"
                    icon={<MapPin size={20} />}
                    error={errors.address}
                    validation={{
                      required: true,
                      minLength: 10,
                      maxLength: 200
                    }}
                  />
                </div>
                
                <div className="md:col-span-2">
                  <SecureInput
                    label="Referral Code (Optional)"
                    type="text"
                    placeholder="Enter referral code if you have one"
                    value={formData.referralCode}
                    onChange={handleChange}
                    name="referralCode"
                    icon={<Users size={20} />}
                    error={errors.referralCode}
                    validation={{
                      required: false,
                      maxLength: 20,
                      pattern: /^[a-zA-Z0-9_-]*$/
                    }}
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    Have a referral code? Enter it to get special benefits and bonuses.
                  </p>
                </div>
                
                <div className="md:col-span-2">
                  <div className="flex items-start gap-3 p-4 rounded-xl bg-gray-800/30 border border-gray-700/30">
                    <input
                      type="checkbox"
                      id="terms"
                      checked={acceptedTerms}
                      onChange={(e) => setAcceptedTerms(e.target.checked)}
                      className="mt-1 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <label htmlFor="terms" className="text-sm text-gray-300 leading-relaxed">
                      I agree to the{" "}
                      <Link href="/terms" className="text-blue-400 hover:text-blue-300 underline">
                        Terms of Service
                      </Link>{" "}
                      and{" "}
                      <Link href="/privacy" className="text-blue-400 hover:text-blue-300 underline">
                        Privacy Policy
                      </Link>
                    </label>
                  </div>
                  {errors.terms && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center gap-2 text-red-400 text-sm mt-2"
                    >
                      <AlertCircle size={16} />
                      {errors.terms}
                    </motion.div>
                  )}
                </div>

                {/* CAPTCHA Section */}
                <div className="md:col-span-2">
                  <ReCaptcha onVerify={handleRecaptcha} />
                  {errors.recaptcha && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center gap-2 text-red-400 text-sm mt-2"
                    >
                      <AlertCircle size={16} />
                      {errors.recaptcha}
                    </motion.div>
                  )}
                </div>
                
                <motion.div
                  whileHover={{ scale: isLoading ? 1 : 1.02 }}
                  whileTap={{ scale: isLoading ? 1 : 0.98 }}
                  className="md:col-span-2"
                >
                  <button
                    type="submit"
                    disabled={isLoading || !recaptchaToken}
                    className="w-full bg-gradient-to-r from-blue-600 via-blue-700 to-orange-600 text-white font-bold py-4 rounded-xl hover:from-blue-700 hover:via-blue-800 hover:to-orange-700 transition-all duration-300 shadow-lg hover:shadow-blue-500/25 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center text-lg relative overflow-hidden group"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-orange-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <span className="relative z-10">
                      {isLoading ? (
                        <>
                          <ProfessionalSpinner />
                          Creating Account...
                        </>
                      ) : (
                        'Create Professional Account'
                      )}
                    </span>
                  </button>
                </motion.div>
              </form>

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="mt-8 space-y-6"
              >
                <div className="flex items-center justify-center text-sm">
                  <p className="text-gray-400">
                    Already have an account?{" "}
                    <Link href="/sigin" className="text-blue-400 hover:text-blue-300 hover:underline transition duration-300 font-medium">
                      Sign In
                    </Link>
                  </p>
                </div>
                
                {/* Security badges - Hidden on mobile */}
                <div className="hidden md:block space-y-3">
                  <SecurityBadge
                    icon={Shield}
                    title="Enterprise Security"
                    description="Bank-level encryption and security protocols"
                  />
                  <SecurityBadge
                    icon={CheckCircle}
                    title="Verified Platform"
                    description="Regulated and compliant trading environment"
                  />
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Side Content - Hidden on mobile */}
          <div className="hidden lg:block space-y-8 order-1 lg:order-2">
            <TradingPairs />
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50 shadow-xl"
            >
              <h3 className="text-xl font-bold text-gray-100 mb-4 flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-500/10 border border-blue-500/20">
                  <Award size={20} className="text-blue-400" />
                </div>
                Platform Benefits
              </h3>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 rounded-xl bg-gray-800/30 border border-gray-700/30">
                  <div className="p-2 rounded-lg bg-green-500/10 border border-green-500/20">
                    <Users size={18} className="text-green-400" />
                  </div>
                  <div>
                    <span className="text-gray-200 font-semibold text-sm">10,000+ Active Traders</span>
                    <span className="text-xs text-gray-400 block">Join our growing community</span>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-xl bg-gray-800/30 border border-gray-700/30">
                  <div className="p-2 rounded-lg bg-purple-500/10 border border-purple-500/20">
                    <Zap size={18} className="text-purple-400" />
                  </div>
                  <div>
                    <span className="text-gray-200 font-semibold text-sm">Instant Execution</span>
                    <span className="text-xs text-gray-400 block">Lightning-fast trade processing</span>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-xl bg-gray-800/30 border border-gray-700/30">
                  <div className="p-2 rounded-lg bg-orange-500/10 border border-orange-500/20">
                    <Shield size={18} className="text-orange-400" />
                  </div>
                  <div>
                    <span className="text-gray-200 font-semibold text-sm">Secure Trading</span>
                    <span className="text-xs text-gray-400 block">Advanced security measures</span>
                  </div>
                </div>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-gradient-to-br from-gray-900/90 via-gray-800/85 to-gray-900/90 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50 shadow-xl relative overflow-hidden"
            >
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-blue-500/5"></div>
              <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-orange-500/10 to-transparent rounded-full blur-2xl"></div>
              <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-tr from-blue-500/10 to-transparent rounded-full blur-2xl"></div>
              
              <div className="relative z-10">
                <h3 className="text-xl font-bold text-gray-100 mb-4 flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-orange-500/20 to-blue-500/20 border border-orange-500/30">
                    <Users size={20} className="text-orange-400" />
                  </div>
                  Referral Benefits
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-gray-800/40 to-gray-700/40 hover:from-gray-700/50 hover:to-gray-600/50 transition-all duration-300 border border-gray-700/30 hover:border-gray-600/40 backdrop-blur-sm group">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-orange-500/15 to-yellow-500/15 border border-orange-500/25 group-hover:border-yellow-500/25 transition-all duration-300">
                      <Award size={18} className="text-orange-400 group-hover:text-yellow-400 transition-colors duration-300" />
                    </div>
                    <div>
                      <span className="text-gray-200 font-semibold text-sm group-hover:text-gray-100 transition-colors duration-300">Welcome Bonus</span>
                      <span className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors duration-300 block">Get $50 bonus on signup with referral</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-gray-800/40 to-gray-700/40 hover:from-gray-700/50 hover:to-gray-600/50 transition-all duration-300 border border-gray-700/30 hover:border-gray-600/40 backdrop-blur-sm group">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500/15 to-cyan-500/15 border border-blue-500/25 group-hover:border-cyan-500/25 transition-all duration-300">
                      <Zap size={18} className="text-blue-400 group-hover:text-cyan-400 transition-colors duration-300" />
                    </div>
                    <div>
                      <span className="text-gray-200 font-semibold text-sm group-hover:text-gray-100 transition-colors duration-300">Reduced Fees</span>
                      <span className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors duration-300 block">Enjoy 50% lower trading fees</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-gray-800/40 to-gray-700/40 hover:from-gray-700/50 hover:to-gray-600/50 transition-all duration-300 border border-gray-700/30 hover:border-gray-600/40 backdrop-blur-sm group">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-green-500/15 to-emerald-500/15 border border-green-500/25 group-hover:border-emerald-500/25 transition-all duration-300">
                      <Shield size={18} className="text-green-400 group-hover:text-emerald-400 transition-colors duration-300" />
                    </div>
                    <div>
                      <span className="text-gray-200 font-semibold text-sm group-hover:text-gray-100 transition-colors duration-300">Priority Support</span>
                      <span className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors duration-300 block">Get VIP customer support access</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </>
  );
}

