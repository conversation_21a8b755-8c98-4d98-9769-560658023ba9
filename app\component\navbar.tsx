'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Menu, ChevronRight } from 'lucide-react';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { useRouter, usePathname } from 'next/navigation';
import './navbar.css';

export function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    setMounted(true);
    const token = localStorage.getItem('accessToken');
    setIsLoggedIn(!!token);

    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavigation = (path: string) => {
    // For hash links on the landing page
    if (path.startsWith('#')) {
      const element = document.querySelector(path);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
        return;
      }
    }

    // Ensure the path starts with a forward slash
    const normalizedPath = path.startsWith('/') ? path : `/${path}`;
    window.location.href = normalizedPath;
  };

  const navItems = [
    { label: 'Home', href: '/' },
    { label: 'Pricing', href: '#pricing' },
    { label: 'FAQs', href: '#faqs' },
    { label: 'Live Market', href: '#live-market' }
  ];

  // Prevent hydration issues by not rendering until mounted
  if (!mounted) {
    return (
      <nav className="w-full z-40 bg-transparent transition-all duration-300">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center cursor-pointer">
              <Image
                src="/logo.svg"
                alt="Logo"
                width={24}
                height={20}
                className="w-12 h-12 object-contain"
                priority
              />
              <span className="ml-3 font-bold text-sm sm:text-lg">
                <span className="text-blue-500">Funded </span>
                <span className="text-orange-500">Horizon</span>
              </span>
            </div>
          </div>
        </div>
      </nav>
    );
  }

  return (
    <nav
      aria-label="Main navigation"
      className={`w-full z-40 transition-all duration-300 sticky top-0 ${
      isScrolled 
          ? 'bg-[#0A0F1C]/95 backdrop-blur-md border-b border-white/10 shadow-lg'
        : 'bg-transparent'
      }`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-20 items-center justify-between">
          {/* Logo */}
          <div
            onClick={() => handleNavigation('/')}
            className="flex items-center cursor-pointer transition-transform duration-300 hover:scale-110 logo-container"
            aria-label="Go to homepage"
            tabIndex={0}
            role="button"
          >
            <Image
              src="/logo.svg"
              alt="Funded Horizon Logo"
              width={40}
              height={40}
              className="w-14 h-14 object-contain"
              priority
            />
            <span className="ml-3 font-bold text-base sm:text-xl">
              <span className="text-white">Funded </span>
              <span className="text-orange-500">Horizon</span>
            </span>
          </div>
          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navItems.map(({ label, href }) => (
              <button
                type="button"
                key={label}
                onClick={() => handleNavigation(href)}
                className={`relative text-base font-semibold transition-colors duration-200 px-2 py-1 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2
                  ${
                    (href === pathname || (pathname === '/' && href === '/'))
                      ? 'text-orange-500 nav-active'
                      : 'text-white/90 hover:text-orange-400'
                  }
                `}
                aria-current={href === pathname ? 'page' : undefined}
                aria-label={label}
              >
                {label}
                {(href === pathname || (pathname === '/' && href === '/')) && (
                  <span className="absolute left-0 -bottom-1 w-full h-0.5 bg-gradient-to-r from-orange-500 to-blue-600 rounded-full"></span>
                )}
              </button>
            ))}
          </div>
          {/* Client Area */}
          <div className="flex items-center gap-4">
            {isLoggedIn ? (
              <button
                type="button"
                onClick={() => handleNavigation('/dashboard')}
                className="hidden sm:inline-flex bg-gradient-to-r from-orange-500 to-blue-600 text-white px-5 py-2 rounded-full text-base font-semibold shadow-md hover:brightness-110 hover:scale-105 transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2"
                aria-label="Go to Dashboard"
              >
                Dashboard <ChevronRight className="ml-2 w-4 h-4" />
              </button>
            ) : (
              <>
                <button
                  type="button"
                  onClick={() => handleNavigation('/signup')}
                  className="hidden sm:inline-flex bg-gradient-to-r from-orange-500 to-blue-600 text-white px-5 py-2 rounded-full text-base font-semibold shadow-md hover:brightness-110 hover:scale-105 transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2"
                  aria-label="Sign Up"
                >
                  Sign Up <ChevronRight className="ml-2 w-4 h-4" />
                </button>
                <button
                  type="button"
                  onClick={() => handleNavigation('/sigin')}
                  className="hidden sm:flex bg-white/10 text-white px-5 py-2 rounded-full text-base font-semibold border border-white/10 hover:bg-white/20 hover:text-orange-400 shadow-md transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2"
                  aria-label="Login"
                >
                  Login
                </button>
              </>
            )}
            {/* Mobile Menu */}
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon" className="lg:hidden rounded-lg border border-white/10 bg-white/5 hover:bg-white/10 transition-all duration-300">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[300px] bg-[#0A0F1C] border-white/10">
                <div className="flex flex-col space-y-6 mt-10">
                  {navItems.map(({ label, href }) => (
                    <button
                      type="button"
                      key={label}
                      onClick={() => handleNavigation(href)}
                      className="text-lg text-white/90 hover:text-orange-400 transition-all duration-300 py-3 px-6 rounded-xl hover:bg-white/5 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2"
                      aria-label={label}
                    >
                      {label}
                    </button>
                  ))}
                  <div className="pt-8 px-6 space-y-4">
                    {isLoggedIn ? (
                      <button
                        type="button"
                        onClick={() => handleNavigation('/dashboard')}
                        className="bg-gradient-to-r from-orange-500 to-blue-600 text-white w-full rounded-xl py-3 text-base font-semibold shadow-md transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2"
                        aria-label="Go to Dashboard"
                      >
                        Dashboard <ChevronRight className="ml-2 w-4 h-4 inline" />
                      </button>
                    ) : (
                      <>
                        <button
                          type="button"
                          onClick={() => handleNavigation('/signup')}
                          className="bg-gradient-to-r from-orange-500 to-blue-600 text-white w-full rounded-xl py-3 text-base font-semibold shadow-md transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2"
                          aria-label="Sign Up"
                        >
                          Sign Up <ChevronRight className="ml-2 w-4 h-4 inline" />
                        </button>
                        <button
                          type="button"
                          onClick={() => handleNavigation('/sigin')}
                          className="bg-white/10 text-white w-full rounded-xl py-3 text-base font-semibold border border-white/10 hover:bg-white/20 hover:text-orange-400 shadow-md transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2"
                          aria-label="Login"
                        >
                          Login
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
}
