"use client"

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ArrowRight, CheckCircle, Target, Award, Users } from 'lucide-react'
import { useRouter } from 'next/navigation'

const ACCOUNT_SIZES = [
  "$1,000", "$3,000", "$5,000", "$10,000", "$25,000",
  "$50,000", "$100,000", "$200,000", "$500,000"
] as const

type AccountSize = typeof ACCOUNT_SIZES[number]

interface ChallengeRules {
  "Profit Target": string
  "Profit Second target"?: string
  "Daily Drawdown": string
  "Max Drawdown": string
  "Profit Split": string
  "Min Trading Days": string
  "Leverage": string
}

interface ChallengeType {
  color: string
  description: string
  rules: ChallengeRules
  prices: Record<AccountSize, string>
  salePrice?: Record<AccountSize, string>
  savings: Record<AccountSize, string>
  discountPercentage: Record<AccountSize, string>
}

const CHALLENGE_TYPES: Record<string, ChallengeType> = {
  'HFT NEO': {
    color: "from-purple-600 to-purple-800",
    description: "For elite traders seeking maximum performance",
    rules: {
      "Profit Target": "8%",
      "Daily Drawdown": "5%",
      "Max Drawdown": "10%",
      "Profit Split": "Up to 90%",
      "Min Trading Days": "0",
      "Leverage": "1:100"
    },
    prices: {
      "$1,000": "$20.00",
      "$3,000": "$35.00",
      "$5,000": "$52.50",
      "$10,000": "$87.50",
      "$25,000": "$166.25",
      "$50,000": "$316.67",
      "$100,000": "$550.00",
      "$200,000": "$916.67",
      "$500,000": "$1,683.33"
    },
    salePrice: {
      "$1,000": "$16",
      "$3,000": "$28",
      "$5,000": "$42",
      "$10,000": "$70",
      "$25,000": "$133",
      "$50,000": "$95",
      "$100,000": "$165",
      "$200,000": "$275",
      "$500,000": "$505"
    },
    discountPercentage: {
      "$1,000": "20% OFF",
      "$3,000": "20% OFF",
      "$5,000": "20% OFF",
      "$10,000": "20% OFF",
      "$25,000": "20% OFF",
      "$50,000": "70% OFF",
      "$100,000": "70% OFF",
      "$200,000": "70% OFF",
      "$500,000": "70% OFF"
    },
    savings: {
      "$1,000": "Save $4.00",
      "$3,000": "Save $7.00",
      "$5,000": "Save $10.50",
      "$10,000": "Save $17.50",
      "$25,000": "Save $33.25",
      "$50,000": "Save $221.67",
      "$100,000": "Save $385.00",
      "$200,000": "Save $641.67",
      "$500,000": "Save $1,178.33"
    }
  },
  'One-Step': {
    color: "from-orange-500 to-orange-700",
    description: "Advanced challenge for consistent traders",
    rules: {
      "Profit Target": "10%",
      "Daily Drawdown": "4%",
      "Max Drawdown": "10%",
      "Profit Split": "Up to 80%",
      "Min Trading Days": "5",
      "Leverage": "1:100"
    },
    prices: {
      "$1,000": "$12.50",
      "$3,000": "$21.25",
      "$5,000": "$35.00",
      "$10,000": "$56.25",
      "$25,000": "$110.00",
      "$50,000": "$206.67",
      "$100,000": "$283.33",
      "$200,000": "$516.67",
      "$500,000": "$950.00"
    },
    salePrice: {
      "$1,000": "$10",
      "$3,000": "$17",
      "$5,000": "$28",
      "$10,000": "$45",
      "$25,000": "$88",
      "$50,000": "$62",
      "$100,000": "$85",
      "$200,000": "$155",
      "$500,000": "$285"
    },
    discountPercentage: {
      "$1,000": "20% OFF",
      "$3,000": "20% OFF",
      "$5,000": "20% OFF",
      "$10,000": "20% OFF",
      "$25,000": "20% OFF",
      "$50,000": "70% OFF",
      "$100,000": "70% OFF",
      "$200,000": "70% OFF",
      "$500,000": "70% OFF"
    },
    savings: {
      "$1,000": "Save $2.50",
      "$3,000": "Save $4.25",
      "$5,000": "Save $7.00",
      "$10,000": "Save $11.25",
      "$25,000": "Save $22.00",
      "$50,000": "Save $144.67",
      "$100,000": "Save $198.33",
      "$200,000": "Save $361.67",
      "$500,000": "Save $665.00"
    }
  },
  'Two-Step': {
    color: "from-blue-600 to-blue-800",
    description: "Perfect starting point for new traders",
    rules: {
      "Profit Target": "10%",
      "Profit Second target": "5%",
      "Daily Drawdown": "4%",
      "Max Drawdown": "10%",
      "Profit Split": "Up to 80%",
      "Min Trading Days": "5",
      "Leverage": "1:100"
    },
    prices: {
      "$1,000": "$10.00",
      "$3,000": "$16.25",
      "$5,000": "$28.75",
      "$10,000": "$43.75",
      "$25,000": "$91.25",
      "$50,000": "$150.00",
      "$100,000": "$240.00",
      "$200,000": "$383.33",
      "$500,000": "$740.00"
    },
    salePrice: {
      "$1,000": "$8",
      "$3,000": "$13",
      "$5,000": "$23",
      "$10,000": "$35",
      "$25,000": "$73",
      "$50,000": "$45",
      "$100,000": "$72",
      "$200,000": "$115",
      "$500,000": "$222"
    },
    discountPercentage: {
      "$1,000": "20% OFF",
      "$3,000": "20% OFF",
      "$5,000": "20% OFF",
      "$10,000": "20% OFF",
      "$25,000": "20% OFF",
      "$50,000": "70% OFF",
      "$100,000": "70% OFF",
      "$200,000": "70% OFF",
      "$500,000": "70% OFF"
    },
    savings: {
      "$1,000": "Save $2.00",
      "$3,000": "Save $3.25",
      "$5,000": "Save $5.75",
      "$10,000": "Save $8.75",
      "$25,000": "Save $18.25",
      "$50,000": "Save $105.00",
      "$100,000": "Save $168.00",
      "$200,000": "Save $268.33",
      "$500,000": "Save $518.00"
    }
  }
}

function useIsIPhone() {
  const [isIPhone, setIsIPhone] = useState(false);
  useEffect(() => {
    setIsIPhone(/iPhone|iPad|iPod/.test(navigator.userAgent));
  }, []);
  return isIPhone;
}

export default function TradingChallenge({ isDashboard = false }) {
  const isIPhone = useIsIPhone();
  const [selectedBalance, setSelectedBalance] = useState<AccountSize>("$10,000")
  const router = useRouter();

  return (
    <section className={`${isIPhone ? 'min-h-screen-ios' : 'min-h-screen'} relative py-20 bg-gradient-to-b from-[#0A0F1C] to-black`}>
      <div className="container mx-auto px-4">
        {/* Simple Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-orange-500 via-blue-500 to-blue-600 bg-clip-text text-transparent mb-6">
            Choose Your Challenge
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Select from our range of trading challenges designed to match your experience level
          </p>
        </div>

        {/* Account Size Selector */}
        <div className="mb-12">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-white mb-2">Select Account Size</h3>
            <p className="text-gray-400">Choose your preferred funding amount</p>
          </div>
          
          <div className="flex flex-wrap justify-center gap-3 max-w-4xl mx-auto">
            {ACCOUNT_SIZES.map((size) => (
              <button
                key={size}
                onClick={() => setSelectedBalance(size)}
                className={`px-6 py-3 rounded-lg transition-all duration-300 font-medium ${
                  selectedBalance === size
                    ? "bg-gradient-to-r from-orange-500 to-blue-600 text-white shadow-lg"
                    : "bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white border border-white/10"
                }`}
              >
                {size}
              </button>
            ))}
          </div>
        </div>

        {/* Challenge Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {Object.entries(CHALLENGE_TYPES).map(([name, data]) => (
            <div
              key={name}
              className="bg-gradient-to-br from-[#1a1f2d] to-[#252a3a] rounded-xl border border-white/10 overflow-hidden hover:border-orange-500/30 transition-all duration-300"
            >
              {/* Card Header */}
              <div className={`p-6 bg-gradient-to-r ${data.color}`}>
                <h3 className="text-2xl font-bold text-white mb-2">{name}</h3>
                <p className="text-white/80">{data.description}</p>
              </div>

              {/* Card Content */}
              <div className="p-6">
                {/* Price Section */}
                <div className="mb-6 p-4 bg-green-500/10 rounded-lg border border-green-500/20">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-400">Challenge Price</span>
                    <span className="text-xs font-bold text-orange-500 bg-orange-500/20 px-2 py-1 rounded">
                      {data.discountPercentage[selectedBalance]}
                    </span>
                  </div>
                  
                  <div className="flex items-baseline gap-3">
                    <span className="text-xl text-gray-500 line-through">
                      {data.prices[selectedBalance]}
                    </span>
                    <span className="text-2xl font-bold text-green-400">
                      {data.salePrice?.[selectedBalance] || data.prices[selectedBalance]}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2 mt-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-sm text-green-300">
                      {data.savings[selectedBalance]}
                    </span>
                  </div>
                </div>

                {/* Rules Section */}
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                    <Award className="w-5 h-5 text-orange-400" />
                    Challenge Rules
                  </h4>
                  <div className="space-y-3">
                    {Object.entries(data.rules).map(([rule, value]) => (
                      <div
                        key={rule}
                        className="flex items-center justify-between p-3 bg-white/5 rounded-lg"
                      >
                        <span className="text-gray-400 text-sm">{rule}</span>
                        <span className="text-white font-semibold">{value}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* CTA Button */}
                <button
                  onClick={() => {
                    if (isDashboard) {
                      router.push('/dashboard/buy');
                    } else {
                      window.location.href = '/signup';
                    }
                  }}
                  className={`w-full bg-gradient-to-r ${data.color} text-white py-3 rounded-lg font-semibold flex items-center justify-center gap-2 transition-all duration-300 hover:brightness-110`}
                >
                  Start Challenge
                  <ArrowRight className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Simple Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-orange-500/10 to-blue-500/10 rounded-xl p-8 border border-orange-500/20">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Start Your Trading Journey?
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Join thousands of successful traders who have already passed our challenges.
            </p>
            <button
              onClick={() => {
                if (isDashboard) {
                  router.push('/dashboard/buy');
                } else {
                  window.location.href = '/signup';
                }
              }}
              className="bg-gradient-to-r from-orange-500 to-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:brightness-110 transition-all duration-300"
            >
              Get Started Today
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}
