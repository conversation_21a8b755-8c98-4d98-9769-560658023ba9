'use client'

import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { useState, useEffect, useRef } from 'react'
import { ChevronLeft, ChevronRight, Award, DollarSign, TrendingUp, Play, Pause, Star, Zap, Target, Crown } from 'lucide-react'

export function EnhancedPayoutSection() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [isHovered, setIsHovered] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  })

  const y = useTransform(scrollYProgress, [0, 1], [100, -100])
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0])

  const payoutCertificates = [
    {
      name: "Kabiru Sani",
      image: "/payout/Kabiru Sani payout certificate-02.jpg",
      amount: "$12,450",
      profit: "+24.5%",
      date: "Recent",
      badge: "Elite Trader",
      rank: "Diamond"
    },
    {
      name: "Farhad Mehrabi",
      image: "/payout/Farhad Mehrabi payout certificate-02.jpg",
      amount: "$8,320",
      profit: "+18.2%",
      date: "Recent",
      badge: "Pro Trader",
      rank: "Platinum"
    },
    {
      name: "Musa Okello",
      image: "/payout/Musa Okello payout certificate-02-02.jpg",
      amount: "$15,780",
      profit: "+31.4%",
      date: "Recent",
      badge: "Master Trader",
      rank: "Diamond"
    },
    {
      name: "Tariq Anwar",
      image: "/payout/Tariq Anwar payout certificate-02.jpg",
      amount: "$6,940",
      profit: "+15.8%",
      date: "Recent",
      badge: "Elite Trader",
      rank: "Gold"
    },
    {
      name: "Ajmal Haidar",
      image: "/payout/Ajmal Haidar payout certificate-02.jpg",
      amount: "$9,650",
      profit: "+22.1%",
      date: "Recent",
      badge: "Pro Trader",
      rank: "Platinum"
    },
    {
      name: "Emre Kaya",
      image: "/payout/Emre Kaya payout certificate-02.jpg",
      amount: "$11,200",
      profit: "+28.7%",
      date: "Recent",
      badge: "Master Trader",
      rank: "Diamond"
    },
    {
      name: "Omar Benyamina",
      image: "/payout/Omar Benyamina payout certificate-02.jpg",
      amount: "$7,890",
      profit: "+19.3%",
      date: "Recent",
      badge: "Elite Trader",
      rank: "Gold"
    },
    {
      name: "Faisal Khan",
      image: "/payout/Faisal Khan payout certificate-02.jpg",
      amount: "$13,450",
      profit: "+26.8%",
      date: "Recent",
      badge: "Master Trader",
      rank: "Diamond"
    },
    {
      name: "Abubakar Nsubuga",
      image: "/payout/Abubakar Nsubuga payout certificate-02.jpg",
      amount: "$10,120",
      profit: "+21.4%",
      date: "Recent",
      badge: "Pro Trader",
      rank: "Platinum"
    },
    {
      name: "Hassan Dogo",
      image: "/payout/Hassan Dogo payout certificate-02.jpg",
      amount: "$8,760",
      profit: "+17.9%",
      date: "Recent",
      badge: "Elite Trader",
      rank: "Gold"
    },
    {
      name: "Ismail Geddi",
      image: "/payout/Ismail Geddi  payout certificate-02.jpg",
      amount: "$14,230",
      profit: "+29.2%",
      date: "Recent",
      badge: "Master Trader",
      rank: "Diamond"
    }
  ]

  // Auto-sliding functionality
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === payoutCertificates.length - 1 ? 0 : prevIndex + 1
      )
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, payoutCertificates.length])

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === payoutCertificates.length - 1 ? 0 : prevIndex + 1
    )
  }

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? payoutCertificates.length - 1 : prevIndex - 1
    )
  }

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying)
  }

  const getRankColor = (rank: string) => {
    switch (rank) {
      case 'Diamond': return 'from-purple-500 to-pink-500'
      case 'Platinum': return 'from-gray-400 to-gray-600'
      case 'Gold': return 'from-yellow-400 to-orange-500'
      default: return 'from-blue-500 to-cyan-500'
    }
  }

  return (
    <section ref={containerRef} className="section-tight relative overflow-hidden bg-gradient-to-b from-[#0A0F1C] via-[#0F1420] to-black">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        {/* Animated gradient backgrounds */}
        <motion.div
          animate={{
            opacity: [0.1, 0.4, 0.1],
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          className="absolute top-0 right-0 w-[800px] h-[800px] bg-gradient-to-br from-orange-500/20 via-red-500/10 to-orange-500/20 rounded-full blur-[150px]"
        />
        <motion.div
          animate={{
            opacity: [0.1, 0.3, 0.1],
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
          className="absolute bottom-0 left-0 w-[800px] h-[800px] bg-gradient-to-tr from-blue-500/20 via-purple-500/10 to-blue-500/20 rounded-full blur-[150px]"
        />
        
        {/* Floating particles */}
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -20, 0],
              x: [0, Math.sin(i) * 10, 0],
              opacity: [0.3, 1, 0.3],
            }}
            transition={{ 
              duration: 3 + Math.random() * 2, 
              repeat: Infinity, 
              delay: i * 0.1 
            }}
            className={`absolute w-1 h-1 rounded-full ${
              i % 4 === 0 ? 'bg-orange-400' : 
              i % 4 === 1 ? 'bg-blue-400' : 
              i % 4 === 2 ? 'bg-green-400' : 'bg-purple-400'
            }`}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header Section */}
        <motion.div
          style={{ y, opacity }}
          className="text-center mb-8 md:mb-12"
        >
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="flex items-center justify-center gap-4 mb-6"
          >
            <motion.div
              animate={{ rotate: [0, 15, -15, 0] }}
              transition={{ duration: 3, repeat: Infinity }}
              className="relative"
            >
              <Crown className="w-12 h-12 text-orange-400" />
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute inset-0 bg-orange-400/20 rounded-full blur-md"
              />
            </motion.div>
            
            <h2 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-orange-400 via-yellow-400 to-blue-500 bg-clip-text text-transparent">
              Elite Payout Certificates
            </h2>
            
            <motion.div
              animate={{ rotate: [0, -15, 15, 0] }}
              transition={{ duration: 3, repeat: Infinity, delay: 1 }}
              className="relative"
            >
              <Star className="w-12 h-12 text-blue-400" />
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity, delay: 1 }}
                className="absolute inset-0 bg-blue-400/20 rounded-full blur-md"
              />
            </motion.div>
          </motion.div>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="text-gray-300 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed"
          >
            Witness the extraordinary success of our elite traders through authentic payout certificates. 
            Each certificate represents real profits earned through our advanced trading platform.
          </motion.p>
        </motion.div>

        {/* Main Certificate Display */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="relative max-w-6xl mx-auto mb-16"
        >
          <motion.div
            onHoverStart={() => setIsHovered(true)}
            onHoverEnd={() => setIsHovered(false)}
            className="relative bg-gradient-to-br from-[#1a1f2d]/95 to-[#252a3a]/95 backdrop-blur-2xl rounded-3xl p-8 md:p-12 shadow-2xl border border-orange-500/30 hover:border-orange-500/50 transition-all duration-500"
            style={{
              transformStyle: 'preserve-3d',
              perspective: '1000px'
            }}
          >
            {/* Glow effect */}
            <motion.div
              animate={{
                opacity: isHovered ? 0.4 : 0.1,
                scale: isHovered ? 1.1 : 1,
              }}
              transition={{ duration: 0.5 }}
              className="absolute inset-0 bg-gradient-to-r from-orange-500/20 via-yellow-500/10 to-orange-500/20 rounded-3xl blur-xl"
            />

            {/* Auto-play toggle */}
            <div className="absolute top-6 right-6 z-30">
              <motion.button
                onClick={toggleAutoPlay}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="bg-black/60 hover:bg-black/80 text-white p-3 rounded-full backdrop-blur-sm transition-all duration-300 border border-white/10"
              >
                {isAutoPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
              </motion.button>
            </div>

            {/* Navigation Buttons */}
            <motion.button
              onClick={prevSlide}
              whileHover={{ scale: 1.1, x: -5 }}
              whileTap={{ scale: 0.9 }}
              className="absolute left-6 top-1/2 -translate-y-1/2 z-20 bg-black/60 hover:bg-black/80 text-white p-4 rounded-full backdrop-blur-sm transition-all duration-300 border border-white/10"
            >
              <ChevronLeft className="w-7 h-7" />
            </motion.button>
            
            <motion.button
              onClick={nextSlide}
              whileHover={{ scale: 1.1, x: 5 }}
              whileTap={{ scale: 0.9 }}
              className="absolute right-6 top-1/2 -translate-y-1/2 z-20 bg-black/60 hover:bg-black/80 text-white p-4 rounded-full backdrop-blur-sm transition-all duration-300 border border-white/10"
            >
              <ChevronRight className="w-7 h-7" />
            </motion.button>

            {/* Certificate Image */}
            <div className="relative overflow-hidden rounded-2xl">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentIndex}
                  initial={{ opacity: 0, scale: 1.1, rotateY: 10 }}
                  animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                  exit={{ opacity: 0, scale: 0.9, rotateY: -10 }}
                  transition={{ duration: 0.8, ease: "easeInOut" }}
                  className="relative aspect-[4/3] w-full"
                >
                  <Image
                    src={payoutCertificates[currentIndex].image}
                    alt={`${payoutCertificates[currentIndex].name} Payout Certificate`}
                    fill
                    className="object-cover"
                    priority
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                  
                  {/* Certificate overlay with shimmer effect */}
                  <motion.div
                    animate={{
                      backgroundPosition: ['200% 0', '-200% 0'],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: 'linear'
                    }}
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/15 to-transparent bg-[length:200%_100%]"
                  />

                  {/* Rank Badge */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.5 }}
                    className={`absolute top-4 left-4 bg-gradient-to-r ${getRankColor(payoutCertificates[currentIndex].rank)} text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg border border-white/20`}
                  >
                    {payoutCertificates[currentIndex].rank}
                  </motion.div>

                  {/* Trader Badge */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.7 }}
                    className="absolute top-4 right-4 bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg"
                  >
                    {payoutCertificates[currentIndex].badge}
                  </motion.div>
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Certificate Info */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mt-8 text-center"
            >
              <motion.h3 
                className="text-3xl md:text-4xl font-bold text-white mb-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                {payoutCertificates[currentIndex].name}
              </motion.h3>
              
              <div className="flex items-center justify-center gap-8 mb-6">
                <motion.div 
                  className="flex items-center gap-3 bg-gradient-to-r from-orange-500/20 to-orange-600/20 px-6 py-3 rounded-full border border-orange-500/30"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <DollarSign className="w-6 h-6 text-orange-400" />
                  <span className="text-2xl font-bold text-white">
                    {payoutCertificates[currentIndex].amount}
                  </span>
                </motion.div>
                
                <motion.div 
                  className="flex items-center gap-3 bg-gradient-to-r from-green-500/20 to-green-600/20 px-6 py-3 rounded-full border border-green-500/30"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <TrendingUp className="w-6 h-6 text-green-400" />
                  <span className="text-xl font-semibold text-green-400">
                    {payoutCertificates[currentIndex].profit}
                  </span>
                </motion.div>
              </div>
              
              <motion.p 
                className="text-gray-400 text-lg"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
              >
                Certificate #{currentIndex + 1} of {payoutCertificates.length}
              </motion.p>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Progress Indicators */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
          className="flex justify-center gap-3 mb-12"
        >
          {payoutCertificates.map((_, index) => (
            <motion.button
              key={index}
              onClick={() => setCurrentIndex(index)}
              whileHover={{ scale: 1.3, y: -2 }}
              whileTap={{ scale: 0.9 }}
              className={`w-4 h-4 rounded-full transition-all duration-300 ${
                currentIndex === index 
                  ? 'bg-gradient-to-r from-orange-500 to-yellow-500 scale-150 shadow-lg shadow-orange-500/50' 
                  : 'bg-gray-600 hover:bg-gray-500'
              }`}
            />
          ))}
        </motion.div>

        {/* Thumbnail Gallery */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="max-w-7xl mx-auto"
        >
          <motion.h3 
            className="text-3xl font-bold text-white text-center mb-12"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            All Payout Certificates
          </motion.h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
            {payoutCertificates.map((certificate, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.08, y: -8, rotateY: 5 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setCurrentIndex(index)}
                className={`relative cursor-pointer rounded-xl overflow-hidden border-2 transition-all duration-500 ${
                  currentIndex === index 
                    ? 'border-orange-500 shadow-2xl shadow-orange-500/40' 
                    : 'border-gray-600 hover:border-orange-400/60'
                }`}
                style={{
                  transformStyle: 'preserve-3d',
                }}
              >
                <div className="aspect-[4/3] relative">
                  <Image
                    src={certificate.image}
                    alt={`${certificate.name} Certificate`}
                    fill
                    className="object-cover"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-black/30 hover:bg-black/10 transition-colors duration-300" />
                  
                  {/* Hover overlay */}
                  <motion.div
                    initial={{ opacity: 0 }}
                    whileHover={{ opacity: 1 }}
                    className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"
                  />
                  
                  <div className="absolute bottom-2 left-2 right-2">
                    <div className="bg-black/80 backdrop-blur-sm rounded-lg px-3 py-2">
                      <p className="text-white text-xs font-medium truncate">
                        {certificate.name}
                      </p>
                      <p className="text-orange-400 text-xs font-bold">
                        {certificate.amount}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Enhanced Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6 }}
          className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          <motion.div 
            className="bg-gradient-to-br from-orange-500/10 to-orange-600/10 backdrop-blur-xl rounded-2xl p-8 text-center border border-orange-500/30 relative overflow-hidden"
            whileHover={{ scale: 1.05, y: -10 }}
            whileTap={{ scale: 0.98 }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="absolute top-0 right-0 w-20 h-20 bg-orange-500/10 rounded-full blur-xl"
            />
            <div className="relative z-10">
              <Target className="w-12 h-12 text-orange-400 mx-auto mb-4" />
              <div className="text-4xl font-bold text-orange-400 mb-2">
                {payoutCertificates.length}+
              </div>
              <div className="text-gray-300 text-lg">Certificates Issued</div>
            </div>
          </motion.div>
          
          <motion.div 
            className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 backdrop-blur-xl rounded-2xl p-8 text-center border border-blue-500/30 relative overflow-hidden"
            whileHover={{ scale: 1.05, y: -10 }}
            whileTap={{ scale: 0.98 }}
          >
            <motion.div
              animate={{ rotate: -360 }}
              transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
              className="absolute top-0 left-0 w-20 h-20 bg-blue-500/10 rounded-full blur-xl"
            />
            <div className="relative z-10">
              <DollarSign className="w-12 h-12 text-blue-400 mx-auto mb-4" />
              <div className="text-4xl font-bold text-blue-400 mb-2">
                $100K+
              </div>
              <div className="text-gray-300 text-lg">Total Payouts</div>
            </div>
          </motion.div>
          
          <motion.div 
            className="bg-gradient-to-br from-green-500/10 to-green-600/10 backdrop-blur-xl rounded-2xl p-8 text-center border border-green-500/30 relative overflow-hidden"
            whileHover={{ scale: 1.05, y: -10 }}
            whileTap={{ scale: 0.98 }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
              className="absolute bottom-0 right-0 w-20 h-20 bg-green-500/10 rounded-full blur-xl"
            />
            <div className="relative z-10">
              <TrendingUp className="w-12 h-12 text-green-400 mx-auto mb-4" />
              <div className="text-4xl font-bold text-green-400 mb-2">
                95%+
              </div>
              <div className="text-gray-300 text-lg">Success Rate</div>
            </div>
          </motion.div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8 }}
          className="mt-16 text-center"
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-block bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-2xl hover:shadow-orange-500/25 transition-all duration-300"
          >
            Join Our Elite Traders
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
} 