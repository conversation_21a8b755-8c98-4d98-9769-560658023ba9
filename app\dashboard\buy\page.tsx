// TEST COMMENT
"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Loader2,
  CreditCard,
  User,
  Mail,
  DollarSign,
  ShieldCheck,
  Wallet,
  CheckCircle2,
  Copy,
  Timer,
  Globe,
  Shield,
  Zap,
  Award,
  BadgeCheck,
  Sparkles,
  AlertTriangle,
  ChevronRight,
  ChevronLeft,
  ArrowRight,
  Check,
} from "lucide-react"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { Separator } from "@/components/ui/separator"
import { getApiUrl, secureFetch } from "../../config/api"
import secureStorageAPI from '@/app/lib/secureStorage'

const PAYMENT_METHODS = [
   {
    value: "usdt-bep20",
    label: "USDT (BEP20)",
    image: "/Usdt BEP-20.jpg",
    address: "******************************************",
  },
  {
    value: "usdt-trc20",
    label: "USDT (TRC20)",
    image: "/USDT TRC-20.jpg",
    address: "TASd3e4qaayUoARZ4NWhZy8a4uEwGs3eeX",
  },
  {
    value: "USDT (Polygon PoS/ MATIC)",
    label:"USDT (Polygon PoS/ MATIC)",
    image: "/trc new.jpg",
    address: "******************************************"

  }
 ,
  {
    value: "bnb-bep20",
    label: "BNB (BEP20)",
    image: "/BNB.jpg",
    address: "******************************************",
  },
  {
    value: "eth-erc20",
    label: "ETH (ERC20)",
    image: "/ETH.jpg",
    address: "******************************************",
  },
  {
    value: "sol",
    label: "Solana (SOL)",
    image: "/SOL.jpg",
    address: "6HC7jAKnToyyFuShNteJYZvqYDtGDTu73VpRGkmYQpDJ",
  },
  {
    value: "btc",
    label: "Bitcoin (BTC)",
    image: "/BTC.jpg",
    address: "******************************************",
  },
]

interface ChallengeType {
  value: string
  label: string
  description: string
  features: string[]
  prices: {
    [key: string]: { real: number; discounted: number }
  }
  icon: React.ReactNode
  color: string
}

const CHALLENGE_TYPES: Record<string, ChallengeType> = {
  "HFT Neo": {
    value: "HFT Neo",
    label: "HFT Neo (Discounted)",
    description: "Advanced high-frequency trading program with institutional tools",
    features: ["Advanced algorithms", "Low latency execution", "Professional support"],
    prices: {
      "1000": { real: 20.00, discounted: 16 },
      "3000": { real: 35.00, discounted: 28 },
      "5000": { real: 52.50, discounted: 42 },
      "10000": { real: 87.50, discounted: 70 },
      "25000": { real: 166.25, discounted: 133 },
      "50000": { real: 316.67, discounted: 95 },
      "100000": { real: 550.00, discounted: 165 },
      "200000": { real: 916.67, discounted: 275 },
      "500000": { real: 1683.33, discounted: 505 }
    },
    icon: <Zap className="h-5 w-5" />,
    color: "from-purple-500 to-blue-500",
  },
  "One-Step": {
    value: "One-Step",
    label: "One-Step (Discounted)",
    description: "Intermediate level challenge with balanced risk parameters",
    features: ["Flexible trading style", "Moderate risk limits", "Weekly payouts"],
    prices: {
      "1000": { real: 12.50, discounted: 10 },
      "3000": { real: 21.25, discounted: 17 },
      "5000": { real: 35.00, discounted: 28 },
      "10000": { real: 56.25, discounted: 45 },
      "25000": { real: 110.00, discounted: 88 },
      "50000": { real: 206.67, discounted: 62 },
      "100000": { real: 283.33, discounted: 85 },
      "200000": { real: 516.67, discounted: 155 },
      "500000": { real: 950.00, discounted: 285 }
    },
    icon: <Award className="h-5 w-5" />,
    color: "from-orange-500 to-amber-500",
  },
  "Two-Step": {
    value: "Two-Step",
    label: "Two-Step (Discounted)",
    description: "Entry level program perfect for beginning your journey",
    features: ["Basic analysis tools", "Conservative risk limits", "Learning resources"],
    prices: {
      "1000": { real: 10.00, discounted: 8 },
      "3000": { real: 16.25, discounted: 13 },
      "5000": { real: 28.75, discounted: 23 },
      "10000": { real: 43.75, discounted: 35 },
      "25000": { real: 91.25, discounted: 73 },
      "50000": { real: 150.00, discounted: 45 },
      "100000": { real: 240.00, discounted: 72 },
      "200000": { real: 383.33, discounted: 115 },
      "500000": { real: 740.00, discounted: 222 }
    },
    icon: <BadgeCheck className="h-5 w-5" />,
    color: "from-green-500 to-emerald-500",
  },
}

const STEPS = [
  {
    id: 1,
    title: "Challenge Configuration",
    description: "Select your trading parameters",
    icon: Shield,
  },
  {
    id: 2,
    title: "Personal Information",
    description: "Provide your details",
    icon: User,
  },
  {
    id: 3,
    title: "Payment & Confirmation",
    description: "Complete your purchase",
    icon: CreditCard,
  },
]

export default function BuyPage() {
  const router = useRouter()
  const [mounted, setMounted] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [timeLeft, setTimeLeft] = useState({
    days: 10,
    hours: 0,
    minutes: 0,
    seconds: 0,
  })
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    challenge_type: "",
    account_size: "",
    platform: "",
    payment_method: "usdt-bep20",
    txid: "",
  })
  
  const [selectedMethod, setSelectedMethod] = useState<(typeof PAYMENT_METHODS)[0] | null>(PAYMENT_METHODS[0])
  const [price, setPrice] = useState<number | null>(null)
  const [showCopied, setShowCopied] = useState(false)
  const [paymentProofPreview, setPaymentProofPreview] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    let timerId: NodeJS.Timeout | null = null
    
    const startTimer = () => {
      timerId = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 }
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 }
        } else if (prev.hours > 0) {
          return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 }
        } else if (prev.days > 0) {
          return { ...prev, days: prev.days - 1, hours: 23, minutes: 59, seconds: 59 }
        }
        return prev
      })
    }, 1000)
    }
    
    startTimer()
    
    return () => {
      if (timerId) {
        clearInterval(timerId)
        timerId = null
      }
    }
  }, [])

  const handleCoinSelect = (value: string) => {
    const method = PAYMENT_METHODS.find((m) => m.value === value)
    setSelectedMethod(method || null)
    setFormData((prev) => ({ ...prev, payment_method: value }))
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value, files } = e.target
    if (id === "image" && files && files.length > 0) {
      const file = files[0]
      const previewUrl = URL.createObjectURL(file)
      setPaymentProofPreview(previewUrl)
    } else {
      setFormData((prev) => ({
        ...prev,
        [id]: value,
      }))
    }
  }

  useEffect(() => {
    return () => {
      if (paymentProofPreview) {
        URL.revokeObjectURL(paymentProofPreview)
      }
    }
  }, [paymentProofPreview])

  const handleSelectChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))

    if (field === "challenge_type") {
      const challengeType = Object.values(CHALLENGE_TYPES).find((type) => type.value === value)
      if (challengeType && formData.account_size) {
        setPrice(challengeType.prices[formData.account_size as keyof typeof challengeType.prices]?.discounted || null)
      }
    }

    if (field === "account_size") {
      const challengeType = Object.values(CHALLENGE_TYPES).find((type) => type.value === formData.challenge_type)
      if (challengeType) {
        setPrice(challengeType.prices[value as keyof typeof challengeType.prices]?.discounted || null)
      }
    }
  }

  const handleCopyAddress = async (address: string) => {
    await navigator.clipboard.writeText(address)
    setShowCopied(true)
    setTimeout(() => setShowCopied(false), 2000)
  }

  const validateStep = (step: number) => {
    switch (step) {
      case 1:
        return formData.challenge_type && formData.account_size && formData.platform
      case 2:
        return formData.username.trim() && formData.email.trim()
      case 3:
        const fileInput = document.getElementById('image') as HTMLInputElement
        const file = fileInput?.files?.[0]
        return formData.txid.trim() && file
      default:
        return false
    }
  }

  const canProceedToNext = () => {
    return validateStep(currentStep)
  }

  const canGoBack = () => {
    return currentStep > 1
  }

  const nextStep = () => {
    if (canProceedToNext() && currentStep < 3) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (canGoBack()) {
      setCurrentStep(currentStep - 1)
    }
  }

  const validateForm = () => {
    const fileInput = document.getElementById('image') as HTMLInputElement
    const file = fileInput?.files?.[0]

    const validations = {
      username: formData.username.trim() !== "",
      email: formData.email.trim() !== "",
      challenge_type: formData.challenge_type !== "",
      account_size: formData.account_size !== "",
      platform: formData.platform !== "",
      payment_method: formData.payment_method !== "",
      txid: formData.txid.trim() !== "",
      image: file !== undefined
    }

    const isValid = Object.values(validations).every(v => v === true)

    if (!isValid) {
      const missingFields = Object.entries(validations)
        .filter(([_, valid]) => !valid)
        .map(([field]) => field)
      
      return {
        isValid: false,
        missingFields
      }
    }

    return {
      isValid: true,
      missingFields: []
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const validation = validateForm()
    
    if (!validation.isValid) {
      alert(`Please fill in all required fields: ${validation.missingFields.join(', ')}`)
      return
    }

    setIsSubmitting(true)

    try {
      const form = new FormData()

      const fileInput = document.getElementById('image') as HTMLInputElement
      const file = fileInput?.files?.[0]

      if (!file) {
        alert('Please select an image file')
        setIsSubmitting(false)
        return
      }

      const fields = {
        email: formData.email,
        challenge_type: formData.challenge_type,
        account_size: formData.account_size,
        platform: formData.platform,
        payment_method: formData.payment_method,
        txid: formData.txid,
        username: formData.username,
      }

      Object.entries(fields).forEach(([key, value]) => {
        form.append(key, value)
      })

      form.append('image', file)

      const response = await secureFetch('ordors/order', {
        method: "POST",
        body: form
      })

      const responseText = await response.text()

      if (!response.ok) {
        throw new Error(`Server error: ${responseText}`)
      }

      setShowConfirmation(true)
      setFormData({
        username: "",
        email: "",
        challenge_type: "",
        account_size: "",
        platform: "",
        payment_method: "usdt-bep20",
        txid: "",
      })
      fileInput.value = ''
      setPaymentProofPreview(null)

      setTimeout(() => {
        router.push("/dashboard")
      }, 2000)
    } catch (error) {
      alert('Error submitting order. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!mounted) {
    return null
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6 md:space-y-8"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
              <div className="space-y-2 md:space-y-3">
                <Label className="text-base md:text-lg font-medium text-white">
                  Challenge Type <span className="text-orange-500">*</span>
                </Label>
                <Select
                  onValueChange={(value) => handleSelectChange("challenge_type", value)}
                  value={formData.challenge_type}
                  required
                >
                  <SelectTrigger className="h-12 md:h-14 bg-gray-900/50 border-blue-500/20 rounded-xl text-white">
                    <SelectValue placeholder="Select your challenge" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-blue-500/20 text-white">
                    {Object.entries(CHALLENGE_TYPES).map(([name, type]) => (
                      <SelectItem key={type.value} value={type.value} className="text-white">
                        <div className="py-2 flex items-center gap-2">
                          <div
                            className={`w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center bg-gradient-to-r ${type.color}`}
                          >
                            {type.icon}
                          </div>
                          <div>
                            <div className="font-semibold text-white text-sm md:text-base">{name}</div>
                            <div className="text-xs md:text-sm text-gray-300">{type.description}</div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 md:space-y-3">
                <Label className="text-base md:text-lg font-medium text-white">
                  Account Size <span className="text-orange-500">*</span>
                </Label>
                <Select
                  onValueChange={(value) => handleSelectChange("account_size", value)}
                  value={formData.account_size}
                  required
                >
                  <SelectTrigger className="h-12 md:h-14 bg-gray-900/50 border-blue-500/20 rounded-xl text-white">
                    <SelectValue placeholder="Select account size" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-blue-500/20 text-white">
                    {["1000", "3000", "5000", "10000", "25000", "50000", "100000", "200000", "500000"].map(
                      (size) => (
                        <SelectItem key={size} value={size} className="text-white">
                          <div className="flex items-center gap-2">
                            <div className="w-6 h-6 md:w-8 md:h-8 rounded-full bg-gradient-to-r from-blue-500 to-orange-500 flex items-center justify-center">
                              <DollarSign className="h-3 w-3 md:h-4 md:w-4 text-blue-500" />
                            </div>
                            <span className="text-white text-sm md:text-base">${size.replace(/\B(?=(\d{3})+(?!\d))/g, ",")}</span>
                          </div>
                        </SelectItem>
                      ),
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 md:space-y-3">
                <Label className="text-base md:text-lg font-medium text-white">
                  Trading Platform <span className="text-orange-500">*</span>
                </Label>
                <Select
                  onValueChange={(value) => handleSelectChange("platform", value)}
                  value={formData.platform}
                  required
                >
                  <SelectTrigger className="h-12 md:h-14 bg-gray-900/50 border-blue-500/20 rounded-xl text-white">
                    <SelectValue placeholder="Choose platform" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-blue-500/20 text-white">
                    <SelectItem value="mt4" className="text-white">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 md:w-8 md:h-8 rounded-full bg-gradient-to-r from-blue-500 to-orange-500 flex items-center justify-center">
                          <Globe className="h-3 w-3 md:h-4 md:w-4 text-blue-400" />
                        </div>
                        <span className="text-white text-sm md:text-base">MetaTrader 4</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="mt5" className="text-white">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 md:w-8 md:h-8 rounded-full bg-gradient-to-r from-blue-500 to-orange-500 flex items-center justify-center">
                          <Globe className="h-3 w-3 md:h-4 md:w-4 text-blue-400" />
                        </div>
                        <span className="text-white text-sm md:text-base">MetaTrader 5</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {formData.challenge_type && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 md:p-6 bg-gradient-to-r from-blue-500/10 to-orange-500/10 rounded-xl border border-blue-500/10"
              >
                <div className="flex items-center gap-3 mb-3 md:mb-4">
                  <div
                    className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center bg-gradient-to-r ${CHALLENGE_TYPES[formData.challenge_type]?.color}`}
                  >
                    {CHALLENGE_TYPES[formData.challenge_type]?.icon}
                  </div>
                  <h3 className="text-lg md:text-xl font-semibold text-white">{formData.challenge_type} Features</h3>
                </div>
                <ul className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-4">
                  {CHALLENGE_TYPES[formData.challenge_type]?.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-3 bg-gray-800/50 p-3 rounded-lg">
                      <CheckCircle2 className="h-4 w-4 md:h-5 md:w-5 text-green-500 flex-shrink-0" />
                      <span className="text-white text-sm md:text-base">{feature}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            )}
          </motion.div>
        )

      case 2:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4 md:space-y-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
              <div className="space-y-2 md:space-y-3">
                <Label htmlFor="username" className="text-base md:text-lg font-medium text-white">
                  Full Name <span className="text-orange-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="username"
                    placeholder="Enter Account Name"
                    className="h-12 md:h-14 pl-12 bg-gray-900/50 border-blue-500/20 focus:border-blue-500 rounded-xl text-base text-white"
                    onChange={handleInputChange}
                    value={formData.username}
                    required
                  />
                  <User className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 md:h-5 md:w-5 text-gray-400" />
                </div>
              </div>

              <div className="space-y-2 md:space-y-3">
                <Label htmlFor="email" className="text-base md:text-lg font-medium text-white">
                  Email Address <span className="text-orange-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    className="h-12 md:h-14 pl-12 bg-gray-900/50 border-blue-500/20 focus:border-blue-500 rounded-xl text-base text-white"
                    onChange={handleInputChange}
                    value={formData.email}
                    required
                  />
                  <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 md:h-5 md:w-5 text-gray-400" />
                </div>
              </div>
            </div>
          </motion.div>
        )

      case 3:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6 md:space-y-8"
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8">
              <div className="space-y-4">
                <Label className="text-base md:text-lg font-medium text-white">
                  Payment Method <span className="text-orange-500">*</span>
                </Label>
                
                <div className="p-3 md:p-4 bg-gray-900/50 rounded-xl border border-blue-500/10">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-white flex items-center justify-center overflow-hidden">
                      <Image
                        src={PAYMENT_METHODS[0].image}
                        alt={PAYMENT_METHODS[0].label}
                        width={40}
                        height={40}
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <p className="font-medium text-white text-sm md:text-base">{PAYMENT_METHODS[0].label}</p>
                      <p className="text-xs md:text-sm text-gray-400">Default payment method</p>
                    </div>
                  </div>
                </div>

                <Select onValueChange={handleCoinSelect} value={formData.payment_method}>
                  <SelectTrigger className="h-12 md:h-14 bg-gray-900/50 border-blue-500/20 rounded-xl text-white">
                    <SelectValue placeholder="Change payment method" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-blue-500/20 text-white">
                    {PAYMENT_METHODS.map((method) => (
                      <SelectItem key={method.value} value={method.value} className="text-white">
                        <div className="flex items-center gap-3 py-1">
                          <div className="w-6 h-6 md:w-8 md:h-8 rounded-full bg-white flex items-center justify-center overflow-hidden">
                            <Image
                              src={method.image}
                              alt={method.label}
                              width={32}
                              height={32}
                              className="object-cover"
                            />
                          </div>
                          <span className="text-white text-sm md:text-base">{method.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {selectedMethod && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="p-4 md:p-6 bg-gray-900/50 rounded-xl space-y-4 border border-blue-500/10 shadow-lg"
                  >
                    <div className="flex justify-center">
                      <div className="bg-white p-3 md:p-4 rounded-xl shadow-lg">
                        <Image
                          src={selectedMethod.image || "/placeholder.svg"}
                          alt={selectedMethod.label}
                          width={120}
                          height={120}
                          className="rounded-lg md:w-[180px] md:h-[180px]"
                          priority
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm text-white">Send payment to:</p>
                      <div className="flex items-center gap-2 bg-gradient-to-r from-blue-500/20 to-orange-500/20 p-3 md:p-4 rounded-lg relative">
                        <code className="text-blue-400 flex-1 break-all text-xs md:text-sm">{selectedMethod.address}</code>
                        <Button
                          variant="ghost"
                          size="icon"
                          type="button"
                          onClick={() => handleCopyAddress(selectedMethod.address)}
                          className="hover:bg-blue-500/20 h-8 w-8 md:h-10 md:w-10"
                        >
                          <Copy className="h-3 w-3 md:h-4 md:w-4 text-white" />
                        </Button>
                        {showCopied && (
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0 }}
                            className="absolute -top-8 right-0 bg-green-500 text-white px-2 md:px-3 py-1 rounded-md text-xs md:text-sm font-medium shadow-lg"
                          >
                            Copied!
                          </motion.div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>

              <div className="space-y-4">
                <Label htmlFor="txid" className="text-base md:text-lg font-medium text-white">
                  Transaction ID <span className="text-orange-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="txid"
                    placeholder="Enter your transaction ID"
                    className="h-12 md:h-14 pl-12 bg-gray-900/50 border-blue-500/20 focus:border-blue-500 rounded-xl text-base text-white"
                    onChange={handleInputChange}
                    value={formData.txid}
                    required
                  />
                  <Wallet className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 md:h-5 md:w-5 text-gray-400" />
                </div>
                {selectedMethod && (
                  <p className="text-xs md:text-sm text-white ml-2">
                    Please enter the transaction ID after sending {selectedMethod.label}
                  </p>
                )}

                <div className="space-y-2 md:space-y-3">
                  <Label htmlFor="image" className="text-base md:text-lg font-medium text-white">
                    Payment Proof <span className="text-orange-500">*</span>
                  </Label>
                  <div className="relative">
                    <Input
                      id="image"
                      name="image"
                      type="file"
                      accept="image/*"
                      className="h-12 md:h-14 pl-12 bg-gray-900/50 border-blue-500/20 focus:border-blue-500 rounded-xl text-base text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-500/20 file:text-blue-400 hover:file:bg-blue-500/30"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) {
                          const previewUrl = URL.createObjectURL(file)
                          setPaymentProofPreview(previewUrl)
                        }
                      }}
                      required
                    />
                    <ShieldCheck className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 md:h-5 md:w-5 text-gray-400" />
                  </div>
                  <p className="text-xs md:text-sm text-gray-400">
                    Please upload a screenshot or image of your payment transaction
                  </p>
                  
                  {paymentProofPreview && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mt-4 p-3 md:p-4 bg-gray-900/50 rounded-xl border border-blue-500/10"
                    >
                      <h3 className="text-white font-medium mb-2 text-sm md:text-base">Payment Proof Preview</h3>
                      <div className="relative w-full aspect-video rounded-lg overflow-hidden">
                        <Image
                          src={paymentProofPreview}
                          alt="Payment proof preview"
                          fill
                          className="object-contain"
                        />
                      </div>
                    </motion.div>
                  )}
                </div>

                {price && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-6 md:mt-8 p-4 md:p-6 bg-gradient-to-r from-blue-500/10 to-orange-500/10 rounded-xl space-y-3 md:space-y-4 border border-blue-500/10 shadow-lg"
                  >
                    <div className="flex justify-between text-white text-sm md:text-base">
                      <span>Regular Price</span>
                      <span className="line-through text-gray-400">${CHALLENGE_TYPES[formData.challenge_type]?.prices[formData.account_size]?.real.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-white text-sm md:text-base">
                      <span>Discounted Price</span>
                      <span className="text-green-400">${CHALLENGE_TYPES[formData.challenge_type]?.prices[formData.account_size]?.discounted.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-white text-sm md:text-base">
                      <span>Processing Fee</span>
                      <span>$0</span>
                    </div>
                    <div className="border-t border-gray-700 pt-3 md:pt-4 flex justify-between text-base md:text-lg font-semibold">
                      <span className="text-white">Total</span>
                      <span className="text-blue-400">${CHALLENGE_TYPES[formData.challenge_type]?.prices[formData.account_size]?.discounted.toFixed(2)}</span>
                    </div>
                  </motion.div>
                )}
              </div>
            </div>

            <div className="mt-4 p-3 md:p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-xl">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 md:h-5 md:w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                <p className="text-yellow-400 font-medium text-xs md:text-sm">
                  Note: If your total payment is less than $10, kindly use USDT (Polygon PoS) network for payment to avoid high transaction fees.
                </p>
              </div>
            </div>
          </motion.div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-blue-950 via-slate-900 to-orange-950 text-white overflow-x-hidden">
      {/* Animated background shapes */}
      <div className="pointer-events-none select-none absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-orange-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tr from-orange-500/20 to-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/10 to-orange-500/10 rounded-full blur-3xl animate-pulse delay-500 -translate-x-1/2 -translate-y-1/2" />
      </div>
      
      <div className="container mx-auto px-4 py-4 md:py-16 relative z-10">
        <div className="max-w-6xl mx-auto shadow-2xl rounded-3xl bg-gradient-to-br from-blue-900/80 via-slate-900/80 to-orange-900/80 border-2 border-blue-500/20 backdrop-blur-xl p-2 md:p-4">
          
          {/* Header Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-6 md:mb-12"
          >
            <div className="relative mb-4 md:mb-6">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="inline-flex items-center gap-2 px-3 md:px-6 py-2 rounded-full bg-gradient-to-r from-blue-500/20 to-orange-500/20 border border-blue-500/30 mb-3 md:mb-4 shadow-lg"
              >
                <Sparkles className="w-4 h-4 md:w-5 md:h-5 text-orange-400 animate-pulse" />
                <span className="text-xs md:text-base text-orange-200 font-semibold tracking-wide">Special Offer</span>
              </motion.div>
              <h1 className="text-3xl md:text-6xl font-extrabold bg-gradient-to-r from-blue-400 via-orange-400 to-blue-400 bg-clip-text text-transparent drop-shadow-lg">
                Funded Horizon
              </h1>
              <motion.div
                className="absolute -z-10 inset-0 blur-3xl opacity-20 rounded-full"
                animate={{
                  background: [
                    "radial-gradient(circle, rgba(59,130,246,0.8) 0%, rgba(59,130,246,0) 70%)",
                    "radial-gradient(circle, rgba(249,115,22,0.8) 0%, rgba(249,115,22,0) 70%)",
                    "radial-gradient(circle, rgba(59,130,246,0.8) 0%, rgba(59,130,246,0) 70%)",
                  ],
                }}
                transition={{ duration: 5, repeat: Number.POSITIVE_INFINITY }}
              />
            </div>
            <p className="text-lg md:text-2xl text-white max-w-2xl mx-auto px-4">
              Join the exclusive community of funded horizon and access institutional-grade capital
            </p>
            <div className="mt-4 md:mt-6 flex items-center justify-center px-4">
              <div className="bg-gradient-to-r from-blue-500/30 to-orange-500/30 backdrop-blur-sm rounded-xl px-4 md:px-6 py-2 md:py-3 text-sm md:text-xl text-white font-semibold flex items-center shadow-lg border border-blue-500/20">
                <Timer className="mr-2 h-4 w-4 md:h-5 md:w-5 text-orange-400" />
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-orange-400">
                  <span className="hidden sm:inline">Special Sale ends in: </span>
                  <span className="sm:hidden">Sale ends: </span>
                  {timeLeft.days}d {timeLeft.hours}h {timeLeft.minutes}m {timeLeft.seconds}s
                </span>
              </div>
            </div>
          </motion.div>

          {/* Progress Steps */}
          <div className="mb-6 md:mb-8 px-2">
            <div className="flex items-center justify-between max-w-4xl mx-auto">
              {STEPS.map((step, index) => {
                const isActive = currentStep === step.id
                const isCompleted = currentStep > step.id
                const Icon = step.icon
                
                return (
                  <div key={step.id} className="flex items-center">
                    <div className="flex flex-col items-center">
                      <div
                        className={`w-8 h-8 md:w-12 md:h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                          isCompleted
                            ? "bg-green-500 border-green-500 text-white"
                            : isActive
                            ? "bg-gradient-to-r from-blue-500 to-orange-500 border-blue-500 text-white"
                            : "bg-gray-800 border-gray-600 text-gray-400"
                        }`}
                      >
                        {isCompleted ? (
                          <Check className="h-4 w-4 md:h-6 md:w-6" />
                        ) : (
                          <Icon className="h-4 w-4 md:h-6 md:w-6" />
                        )}
                      </div>
                      <div className="mt-1 md:mt-2 text-center">
                        <p className={`text-xs md:text-sm font-medium ${isActive ? "text-white" : "text-gray-400"}`}>
                          {step.title}
                        </p>
                        <p className="text-xs text-gray-500 mt-1 hidden md:block">{step.description}</p>
                      </div>
                    </div>
                    {index < STEPS.length - 1 && (
                      <div
                        className={`w-8 md:w-16 h-0.5 mx-2 md:mx-4 transition-all duration-300 ${
                          isCompleted ? "bg-green-500" : "bg-gray-600"
                        }`}
                      />
                    )}
                  </div>
                )
              })}
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6 md:space-y-8">
            {/* Step Content */}
            <Card className="bg-gradient-to-br from-blue-900/60 via-slate-900/60 to-orange-900/60 border border-blue-500/30 shadow-xl overflow-hidden">
              <CardContent className="p-4 md:p-8">
                {renderStepContent()}
              </CardContent>
            </Card>

            {/* Navigation Buttons */}
            <div className="flex justify-between items-center pt-4 md:pt-6 px-2">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                disabled={!canGoBack()}
                className="flex items-center gap-2 px-3 md:px-6 py-2 md:py-3 border-blue-500/30 text-blue-400 hover:bg-blue-500/10 disabled:opacity-50 disabled:cursor-not-allowed text-sm md:text-base"
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="hidden sm:inline">Previous</span>
                <span className="sm:hidden">Prev</span>
              </Button>

              <div className="flex items-center gap-2 md:gap-4">
                {currentStep < 3 ? (
                  <Button
                    type="button"
                    onClick={nextStep}
                    disabled={!canProceedToNext()}
                    className="flex items-center gap-2 px-4 md:px-6 py-2 md:py-3 bg-gradient-to-r from-blue-500 to-orange-500 hover:from-blue-600 hover:to-orange-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm md:text-base"
                  >
                    <span className="hidden sm:inline">Next</span>
                    <span className="sm:hidden">Next</span>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    disabled={isSubmitting || !canProceedToNext()}
                    className="flex items-center gap-2 px-6 md:px-8 py-2 md:py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm md:text-base"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="hidden sm:inline">Processing...</span>
                        <span className="sm:hidden">Processing</span>
                      </>
                    ) : (
                      <>
                        <ShieldCheck className="h-4 w-4" />
                        <span className="hidden sm:inline">Complete Purchase</span>
                        <span className="sm:hidden">Purchase</span>
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </form>
        </div>
      </div>

      <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <DialogContent className="bg-gradient-to-br from-blue-900/80 via-slate-900/80 to-orange-900/80 border-blue-500/30 rounded-xl max-w-md text-white shadow-2xl mx-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center p-4 md:p-6 space-y-4 md:space-y-6"
          >
            <div className="relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ repeat: Number.POSITIVE_INFINITY, duration: 2 }}
                  className="w-16 h-16 md:w-20 md:h-20 bg-green-500/20 rounded-full"
                />
              </div>
              <CheckCircle2 className="h-16 w-16 md:h-20 md:w-20 text-green-500 mx-auto relative z-10" />
            </div>
            <h2 className="text-xl md:text-2xl font-bold text-white">Purchase Successful!</h2>
            <p className="text-white text-sm md:text-base">
              Thank you for joining our trading challenge. Check your email for account details and next steps.
            </p>
            <Button
              onClick={() => setShowConfirmation(false)}
              className="bg-gradient-to-r from-blue-500 to-orange-500 hover:from-blue-600 hover:to-orange-600 w-full h-10 md:h-12 rounded-xl text-white shadow-lg text-sm md:text-base"
            >
              Got it
            </Button>
          </motion.div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
