@tailwind base;
@tailwind components;
@tailwind utilities;

/* iPhone-specific viewport height fix */
:root {
  --vh: 1vh;
}

html, body {
  height: auto !important;
  min-height: 0 !important;
}

body {
  font-family: Arial, Helvetica, sans-serif;
  /* Prevent horizontal scroll on mobile */
  overflow-x: hidden;
  /* Improve touch scrolling on iOS */
  -webkit-overflow-scrolling: touch;
  /* Use custom viewport height for iOS */
  min-height: calc(var(--vh, 1vh) * 100);
}

/* Mobile-specific fixes */
@layer base {
  /* iPhone-specific fixes */
  @supports (-webkit-touch-callout: none) {
    .min-h-screen {
      min-height: -webkit-fill-available;
    }
    
    body {
      min-height: -webkit-fill-available;
    }
    
    /* Fix for iOS Safari 100vh issue */
    .h-screen {
      height: -webkit-fill-available;
    }
  }
  
  /* Prevent zoom on input focus for iOS */
  @media screen and (max-width: 768px) {
    input, select, textarea {
      font-size: 16px !important;
    }
  }
  
  /* Improve touch targets on mobile */
  @media (max-width: 768px) {
    button, a, [role="button"] {
      min-height: 44px;
      min-width: 44px;
    }
  }
  
  /* Improve performance on mobile */
  * {
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Fix for iOS Safari flexbox issues */
  .flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
  
  /* Fix for iOS Safari grid issues */
  .grid {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: grid;
  }
  
  /* Fix for iOS Safari backdrop-filter issues */
  @supports (-webkit-backdrop-filter: none) or (backdrop-filter: none) {
    .backdrop-blur-md {
      -webkit-backdrop-filter: blur(12px);
      backdrop-filter: blur(12px);
    }
    .backdrop-blur {
      -webkit-backdrop-filter: blur(20px);
      backdrop-filter: blur(20px);
    }
  }
  
  /* Fix for iOS Safari transform issues */
  @media (max-width: 768px) {
    .transform {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }
  }
  
  /* Fix for iOS Safari animation performance */
  @media (max-width: 768px) {
    .animate-spin {
      animation-duration: 2s;
    }
    
    .animate-pulse {
      animation-duration: 3s;
    }
  }
  
  /* Fix for iOS Safari overflow issues */
  @media (max-width: 768px) {
    .overflow-hidden {
      -webkit-overflow-scrolling: touch;
    }
  }
  
  /* Fix for iOS Safari position sticky issues */
  @supports (-webkit-touch-callout: none) {
    .sticky {
      position: -webkit-sticky;
      position: sticky;
    }
  }
  
  /* iPhone-specific loading optimizations */
  @media (max-width: 768px) {
    /* Reduce animation complexity on mobile */
    .animate-bounce {
      animation-duration: 2s;
    }

    .animate-ping {
      animation-duration: 3s;
    }

    /* Optimize images for mobile */
    img {
      -webkit-user-select: none;
      -webkit-touch-callout: none;
    }

    /* Improve text rendering on mobile */
    * {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }

  /* iPhone-specific performance optimizations */
  @supports (-webkit-touch-callout: none) {
    /* Disable complex animations on iPhone to prevent overheating */
    [data-disable-heavy-components="1"] .animate-spin {
      animation: none !important;
    }

    [data-disable-heavy-components="1"] .animate-pulse {
      animation: none !important;
    }

    [data-disable-heavy-components="1"] .animate-bounce {
      animation: none !important;
    }

    /* Reduce motion for iPhone */
    [data-reduce-motion="1"] * {
      animation-duration: 0.3s !important;
      transition-duration: 0.3s !important;
    }

    /* Optimize transforms for iPhone */
    [data-disable-heavy-components="1"] [style*="transform"] {
      transform: none !important;
    }

    /* Disable 3D transforms on iPhone */
    [data-disable-heavy-components="1"] [style*="rotateY"],
    [data-disable-heavy-components="1"] [style*="rotateX"],
    [data-disable-heavy-components="1"] [style*="rotateZ"] {
      transform: none !important;
    }
  }
  
  /* iPhone Safari specific fixes */
  @supports (-webkit-touch-callout: none) {
    /* Fix for iOS Safari flexbox bugs */
    .flex-col {
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
    }
    
    .flex-row {
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
      -webkit-flex-direction: row;
      -ms-flex-direction: row;
      flex-direction: row;
    }
    
    /* Fix for iOS Safari justify-content */
    .justify-center {
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
    }
    
    .justify-between {
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }
    
    /* Fix for iOS Safari align-items */
    .items-center {
      -webkit-box-align: center;
      -webkit-align-items: center;
      -ms-flex-align: center;
      align-items: center;
    }
    
    /* Fix for iOS Safari overflow scrolling */
    .overflow-auto {
      -webkit-overflow-scrolling: touch;
    }
    
    .overflow-scroll {
      -webkit-overflow-scrolling: touch;
    }
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Custom viewport height utility for iOS */
  .h-screen-ios {
    height: calc(var(--vh, 1vh) * 100);
  }

  .min-h-screen-ios {
    min-height: calc(var(--vh, 1vh) * 100);
  }

  /* Tight section spacing utilities for cohesive layout */
  .section-tight {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .section-tight-mobile {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .section-gap-tight {
    margin-bottom: 1rem;
  }

  .section-gap-none {
    margin-bottom: 0;
  }

  /* Responsive tight spacing */
  @media (min-width: 768px) {
    .section-tight {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
  }

  @media (min-width: 1024px) {
    .section-tight {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }
  }
}

@layer base {
  :root {
    /* Light mode - professional theme */
    --background: 220 16% 96%;
    --foreground: 222 47% 11%;
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47% 11%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222 47% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 83% 53%;

    /* Chart colors - professional palette */
    --chart-1: 221 83% 53%;
    --chart-2: 226 71% 40%;
    --chart-3: 215 25% 27%;
    --chart-4: 34 100% 34%;
    --chart-5: 0 72% 51%;

    --radius: 0.5rem;

    /* Sidebar - professional theme */
    --sidebar-background: 222 47% 11%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 221 83% 53%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217 33% 17%;
    --sidebar-ring: 221 83% 53%;
  }

  .dark {
    /* Dark mode - professional dark theme */
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;
    --card: 217 33% 17%;
    --card-foreground: 210 40% 98%;
    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 33% 17%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 224.3 76.3% 48%;

    /* Chart colors - professional dark palette */
    --chart-1: 221 83% 53%;
    --chart-2: 34 100% 34%;
    --chart-3: 0 72% 51%;
    --chart-4: 262 83% 58%;
    --chart-5: 199 89% 48%;

    /* Sidebar - professional dark theme */
    --sidebar-background: 222 47% 11%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 221 83% 53%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217 33% 17%;
    --sidebar-ring: 221 83% 53%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
