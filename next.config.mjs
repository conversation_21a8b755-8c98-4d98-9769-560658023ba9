let userConfig;
try {
  userConfig = await import('./v0-user-next.config');
} catch (e) {
  // Ignore error if the user config file doesn't exist
}

const isDev = process.env.NODE_ENV !== 'production';

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone', // Required for Heroku and similar deployments
  reactStrictMode: true, // Helps catch React issues
  trailingSlash: false, // Changed to false to better handle query parameters
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
  compress: true, // Enables gzip and brotli compression
  images: {
    unoptimized: true, // Disable image optimization to save memory
    formats: ['image/webp'], // Use only webp for better compression
    domains: ['fundedhorizon-back-e4285707ccdf.herokuapp.com'], // Allow images from backend
  },
  headers: async () => [
    {
      source: '/(.*)',
      headers: [
        // Security Headers
        {
          key: 'X-Content-Type-Options',
          value: 'nosniff',
        },
        {
          key: 'X-Frame-Options',
          value: 'DENY',
        },
        {
          key: 'X-XSS-Protection',
          value: '1; mode=block',
        },
        {
          key: 'Referrer-Policy',
          value: 'strict-origin-when-cross-origin',
        },
        {
          key: 'Strict-Transport-Security',
          value: 'max-age=31536000; includeSubDomains; preload',
        },
        // Mobile-specific headers
        {
          key: 'X-UA-Compatible',
          value: 'IE=edge',
        },
        // Simplified CSP for production
        {
          key: 'Content-Security-Policy',
          value: isDev
            ? "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://connect.facebook.net https://www.googletagmanager.com https://www.google.com/recaptcha/ https://www.gstatic.com/recaptcha/"
            : "script-src 'self' 'unsafe-inline' https://connect.facebook.net https://www.googletagmanager.com https://www.google.com/recaptcha/ https://www.gstatic.com/recaptcha/"
        },
        {
          key: 'Permissions-Policy',
          value: 'camera=(), microphone=(), geolocation=(), payment=()',
        },
        // Cache Control
        {
          key: 'Cache-Control',
          value: 'public, max-age=3600, s-maxage=86400, stale-while-revalidate',
        },
      ],
    },
    // API routes - no caching
    {
      source: '/api/(.*)',
      headers: [
        {
          key: 'Cache-Control',
          value: 'no-store, no-cache, must-revalidate, proxy-revalidate',
        },
        {
          key: 'Pragma',
          value: 'no-cache',
        },
        {
          key: 'Expires',
          value: '0',
        },
      ],
    },
  ],

  // Add rewrites to handle reset-password with query parameters
  rewrites: async () => [
    {
      source: '/reset-password',
      destination: '/reset-password',
    },
    {
      source: '/reset-password/:path*',
      destination: '/reset-password',
    },
  ],
  
  // Optimize for production and reduce memory usage
  experimental: {
    optimizeCss: true,
    serverMinification: true,
    optimizeServerReact: true,
    // Disable some experimental features to save memory
    webpackBuildWorker: false,
    parallelServerBuildTraces: false,
    parallelServerCompiles: false,
    serverSourceMaps: false,
  },
  
  // Security: Disable source maps in production
  productionBrowserSourceMaps: false,
  // Security: Disable directory listing
  generateEtags: false,
  
  // Webpack optimizations for production
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // Optimize bundle size
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      };
      
      // Optimize for mobile performance
      config.optimization.minimize = true;
    }
    
    // Add mobile-specific optimizations
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };
    
    return config;
  },
};

// Merge user configuration if it exists
if (userConfig) {
  for (const key in userConfig) {
    if (typeof nextConfig[key] === 'object' && !Array.isArray(nextConfig[key])) {
      nextConfig[key] = { ...nextConfig[key], ...userConfig[key] };
    } else {
      nextConfig[key] = userConfig[key];
    }
  }
}

export default nextConfig;
